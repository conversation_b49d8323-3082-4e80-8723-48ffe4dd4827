import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID } from 'class-validator';

export class InviteFriendsByIventIdDto {
  @ApiProperty({
    format: 'uuid',
    description: 'Array of group UUIDs to invite to the ivent',
    example: ['123e4567-e89b-12d3-a456-************'],
  })
  @IsArray()
  @IsUUID('4', { each: true, message: 'Each group ID must be a valid UUID v4' })
  groupIds!: string[];

  @ApiProperty({
    format: 'uuid',
    description: 'Array of user UUIDs to invite to the ivent',
    example: ['123e4567-e89b-12d3-a456-************'],
  })
  @IsArray()
  @IsUUID('4', { each: true, message: 'Each user ID must be a valid UUID v4' })
  userIds!: string[];
}

export class JoinIventAndCreateSquadByIventIdDto {
  @ApiProperty({
    format: 'uuid',
    description: 'Array of group UUIDs to invite to the ivent',
    example: ['123e4567-e89b-12d3-a456-************'],
  })
  @IsArray()
  @IsUUID('4', { each: true, message: 'Each group ID must be a valid UUID v4' })
  groupIds!: string[];

  @ApiProperty({
    format: 'uuid',
    description: 'Array of user UUIDs to invite to the ivent',
    example: ['123e4567-e89b-12d3-a456-************'],
  })
  @IsArray()
  @IsUUID('4', { each: true, message: 'Each user ID must be a valid UUID v4' })
  userIds!: string[];
}
