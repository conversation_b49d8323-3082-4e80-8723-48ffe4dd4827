import {
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  Param,
  ParseIntPipe,
  ParseUUIDPipe,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { IventCollabsService } from './ivent-collabs.service';
import { RemoveCollabByIventIdDto } from './models/ivent-collabs.dto';
import { SearchCollabsForIventCreationReturn, SearchCollabsReturn } from './models/ivent-collabs.returns';

@ApiTags('iventCollabs')
@Controller('iventCollabs')
export class IventCollabsController {
  constructor(private readonly iventCollabsService: IventCollabsService) {}

  @ApiOperation({
    summary: 'Ivent oluştururken paydaş olabilecek hesapları listeler',
  })
  @ApiResponse({
    status: 200,
    type: SearchCollabsForIventCreationReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get('search')
  async searchCollabsForIventCreation(
    @Res({ passthrough: true }) res: Response,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.iventCollabsService.searchCollabsForIventCreation({
      sessionId,
      sessionRole,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
  @ApiOperation({
    summary: "Ivent'te bulunan paydaş hesapları listeler",
  })
  @ApiResponse({
    status: 200,
    type: SearchCollabsReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':iventId/search')
  async searchCollabs(
    @Res({ passthrough: true }) res: Response,
    @Param('iventId', new ParseUUIDPipe({ version: '4' })) iventId: string,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.iventCollabsService.searchCollabs({
      sessionId,
      sessionRole,
      iventId,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Ivent IDsi ile paydaş hesaplardan ayrılınır',
  })
  @ApiResponse({ status: 200 })
  @Post(':iventId/leave')
  async leaveCollabrationByIventId(
    @Res({ passthrough: true }) res: Response,
    @Param('iventId', new ParseUUIDPipe({ version: '4' })) iventId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.iventCollabsService.leaveCollabrationByIventId({
      sessionId,
      sessionRole,
      iventId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Ivent IDsi ile bir hesabı paydaş hesaplardan çıkartır',
  })
  @ApiResponse({ status: 200 })
  @Post(':iventId/remove')
  async removeCollabByIventId(
    @Body() removeCollabByIventCollabIdDto: RemoveCollabByIventIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('iventId', new ParseUUIDPipe({ version: '4' })) iventId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.iventCollabsService.removeCollabByIventId({
      sessionId,
      sessionRole,
      iventId,
      ...removeCollabByIventCollabIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
}
