import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsUUID, IsUrl, MaxLength } from 'class-validator';
import { VibePrivacyEnum } from 'src/entities';
import { MediaFormatEnum } from 'src/entities/enums/shared/media-format-enum';

export class CreateVibeDto {
  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Base64 encoded file buffer for media upload',
    example: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...',
  })
  @IsString()
  @IsOptional()
  fileBuffer?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'URL to the media file if hosted externally',
    example: 'https://example.com/media/video.mp4',
    format: 'url',
  })
  @IsUrl({}, { message: 'Media URL must be a valid URL' })
  @IsOptional()
  mediaUrl?: string | null;

  @ApiProperty({
    enum: MediaFormatEnum,
    enumName: 'MediaFormatEnum',
    description: 'Format of the media being uploaded',
    example: MediaFormatEnum.IMAGE,
  })
  @IsEnum(MediaFormatEnum, { message: 'Media format must be a valid MediaFormatEnum value' })
  mediaFormat!: MediaFormatEnum;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Caption or description for the vibe',
    example: 'Amazing sunset at the beach! 🌅',
    maxLength: 500,
  })
  @IsString()
  @MaxLength(500, { message: 'Caption must be 500 characters or less' })
  @IsOptional()
  caption?: string | null;

  @ApiProperty({
    description: 'UUID of the squad this vibe belongs to',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsUUID('4', { message: 'Squad ID must be a valid UUID v4' })
  squadId!: string;

  @ApiProperty({
    enum: VibePrivacyEnum,
    enumName: 'VibePrivacyEnum',
    description: 'Privacy setting for the vibe',
    example: VibePrivacyEnum.PUBLIC,
  })
  @IsEnum(VibePrivacyEnum, { message: 'Privacy must be a valid VibePrivacyEnum value' })
  privacy!: VibePrivacyEnum;
}

export class UpdateByVibeIdDto {
  @ApiProperty({
    description: 'New caption for the vibe',
    example: 'Updated caption with new information',
    maxLength: 500,
  })
  @IsString()
  @MaxLength(500, { message: 'Caption must be 500 characters or less' })
  newCaption!: string;
}
