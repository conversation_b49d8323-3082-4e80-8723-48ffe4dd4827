class SearchBoxContext {
  final SearchBoxContextRegion? region;
  final SearchBoxContextPostcode? postcode;
  final SearchBoxContextDistrict? district;
  final SearchBoxContextPlace? place;
  final SearchBoxContextLocality? locality;
  final SearchBoxContextNeighborhood? neighborhood;
  final SearchBoxContextAddress? address;
  final SearchBoxContextStreet? street;

  SearchBoxContext({
    required this.region,
    required this.postcode,
    required this.district,
    required this.place,
    required this.locality,
    required this.neighborhood,
    required this.address,
    required this.street,
  });

  factory SearchBoxContext.fromJson(json) {
    return SearchBoxContext(
      region: json['region'] != null ? SearchBoxContextRegion.fromJson(json['region']) : null,
      postcode: json['postcode'] != null ? SearchBoxContextPostcode.fromJson(json['postcode']) : null,
      district: json['district'] != null ? SearchBoxContextDistrict.fromJson(json['district']) : null,
      place: json['place'] != null ? SearchBoxContextPlace.fromJson(json['place']) : null,
      locality: json['locality'] != null ? SearchBoxContextLocality.fromJson(json['locality']) : null,
      neighborhood: json['neighborhood'] != null ? SearchBoxContextNeighborhood.fromJson(json['neighborhood']) : null,
      address: json['address'] != null ? SearchBoxContextAddress.fromJson(json['address']) : null,
      street: json['street'] != null ? SearchBoxContextStreet.fromJson(json['street']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'region': region?.toJson(),
      'postcode': postcode?.toJson(),
      'district': district?.toJson(),
      'place': place?.toJson(),
      'locality': locality?.toJson(),
      'neighborhood': neighborhood?.toJson(),
      'address': address?.toJson(),
      'street': street?.toJson(),
    };
    return jsonObject;
  }
}

class SearchBoxContextRegion {
  final String? id;
  final String? name;
  final String? region_code;
  final String? region_code_full;

  SearchBoxContextRegion({
    required this.id,
    required this.name,
    required this.region_code,
    required this.region_code_full,
  });

  factory SearchBoxContextRegion.fromJson(json) {
    return SearchBoxContextRegion(
      id: json['id'],
      name: json['name'],
      region_code: json['region_code'],
      region_code_full: json['region_code_full'],
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'id': id,
      'name': name,
      'region_code': region_code,
      'region_code_full': region_code_full,
    };
    return jsonObject;
  }
}

class SearchBoxContextPostcode {
  final String? id;
  final String? name;

  SearchBoxContextPostcode({
    required this.id,
    required this.name,
  });

  factory SearchBoxContextPostcode.fromJson(json) {
    return SearchBoxContextPostcode(
      id: json['id'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'id': id,
      'name': name,
    };
    return jsonObject;
  }
}

class SearchBoxContextDistrict {
  final String? id;
  final String? name;

  SearchBoxContextDistrict({
    required this.id,
    required this.name,
  });

  factory SearchBoxContextDistrict.fromJson(json) {
    return SearchBoxContextDistrict(
      id: json['id'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'id': id,
      'name': name,
    };
    return jsonObject;
  }
}

class SearchBoxContextPlace {
  final String? id;
  final String? name;

  SearchBoxContextPlace({
    required this.id,
    required this.name,
  });

  factory SearchBoxContextPlace.fromJson(json) {
    return SearchBoxContextPlace(
      id: json['id'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'id': id,
      'name': name,
    };
    return jsonObject;
  }
}

class SearchBoxContextLocality {
  final String? id;
  final String? name;

  SearchBoxContextLocality({
    required this.id,
    required this.name,
  });

  factory SearchBoxContextLocality.fromJson(json) {
    return SearchBoxContextLocality(
      id: json['id'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'id': id,
      'name': name,
    };
    return jsonObject;
  }
}

class SearchBoxContextNeighborhood {
  final String? id;
  final String? name;

  SearchBoxContextNeighborhood({
    required this.id,
    required this.name,
  });

  factory SearchBoxContextNeighborhood.fromJson(json) {
    return SearchBoxContextNeighborhood(
      id: json['id'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'id': id,
      'name': name,
    };
    return jsonObject;
  }
}

class SearchBoxContextAddress {
  final String? id;
  final String? name;
  final String? address_number;
  final String? street_name;

  SearchBoxContextAddress({
    required this.id,
    required this.name,
    required this.address_number,
    required this.street_name,
  });

  factory SearchBoxContextAddress.fromJson(json) {
    return SearchBoxContextAddress(
      id: json['id'],
      name: json['name'],
      address_number: json['address_number'],
      street_name: json['street_name'],
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'id': id,
      'name': name,
      'address_number': address_number,
      'street_name': street_name,
    };
    return jsonObject;
  }
}

class SearchBoxContextStreet {
  final String? id;
  final String? name;

  SearchBoxContextStreet({
    required this.id,
    required this.name,
  });

  factory SearchBoxContextStreet.fromJson(json) {
    return SearchBoxContextStreet(
      id: json['id'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'id': id,
      'name': name,
    };
    return jsonObject;
  }
}
