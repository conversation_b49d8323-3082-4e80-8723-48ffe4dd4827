import { FriendListingTypeEnum } from "src/constants/enums";

export type InviteFriendsByIventId = {
  sessionId: string;
  sessionRole: string;
  iventId: string;
  groupIds: string[];
  userIds: string[];
};

export type JoinIventAndCreateSquadByIventIdParams = {
  sessionId: string;
  sessionRole: string;
  iventId: string;
  groupIds: string[];
  userIds: string[];
};

export type LeaveSquadByIventId = {
  sessionId: string;
  sessionRole: string;
  iventId: string;
};

export type SearchInvitableUsersByIventIdParams = {
  sessionId: string;
  sessionRole: string;
  iventId: string;
  type: FriendListingTypeEnum;
  q: string;
  limit: number;
  page: number;
};

export type SearchParticipantsByIventIdParams = {
  sessionId: string;
  sessionRole: string;
  iventId: string;
  q: string;
  limit: number;
  page: number;
};
