import {
  Controller,
  DefaultV<PERSON>uePipe,
  Get,
  Param,
  ParseIntPipe,
  ParseUUIDPipe,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { NotificationReplyTypeEnum } from 'src/constants/enums';
import { GetNotificationsReturn } from './models/notifications.returns';
import { NotificationsService } from './notifications.service';

@ApiTags('notifications')
@Controller('notifications')
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @ApiOperation({
    summary: 'Bildirimleri listeler',
  })
  @ApiResponse({
    status: 200,
    type: GetNotificationsReturn,
  })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get('')
  async getNotifications(
    @Res({ passthrough: true }) res: Response,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.notificationsService.getNotifications({
      sessionId,
      sessionRole,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Squad IDsi ile seçilen davete "accept" ya da "reject" yanıtı verilir',
  })
  @ApiResponse({ status: 200 })
  @ApiQuery({
    name: 'reply',

    enum: NotificationReplyTypeEnum,
    enumName: 'NotificationReplyTypeEnum',
    description: 'Reply type for the notification',
    example: NotificationReplyTypeEnum.ACCEPT,
  })
  @Post(':id/reply')
  async replyInvitationBySquadId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) notificationId: string,
    @Query('reply') replyType: NotificationReplyTypeEnum,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.notificationsService.replyInvitationByNotificationId({
      sessionId,
      sessionRole,
      notificationId,
      replyType,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
}
