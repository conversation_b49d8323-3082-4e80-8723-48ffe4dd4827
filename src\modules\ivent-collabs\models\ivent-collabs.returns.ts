import { ApiProperty } from '@nestjs/swagger';
import { CollabratorListItem } from 'src/models';
import { BasicAccountListItem } from 'src/models/basic-account-list-item';

export class SearchCollabsForIventCreationReturn {
  @ApiProperty({
    type: [BasicAccountListItem],
    description: 'List of accounts available for collaboration during ivent creation',
    example: [],
  })
  accounts!: BasicAccountListItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of accounts available for collaboration',
    example: 12,
    minimum: 0,
  })
  accountCount!: number;
}

export class SearchCollabsReturn {
  @ApiProperty({
    type: [CollabratorListItem],
    description: 'List of collaborators with their membership and friendship status',
    example: [],
  })
  collabs!: CollabratorListItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of collaborators found',
    example: 8,
    minimum: 0,
  })
  collabCount!: number;
}
