class SearchBoxGeometry {
  final String type;
  final List<double> coordinates;

  SearchBoxGeometry({
    required this.type,
    required this.coordinates,
  });

  factory SearchBoxGeometry.fromJson(json) {
    return SearchBoxGeometry(
      type: json['type'],
      coordinates: List<double>.from(json['coordinates']),
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'type': type,
      'coordinates': coordinates,
    };
    return jsonObject;
  }
}
