import { ApiProperty } from '@nestjs/swagger';
import { NotificationEnum } from 'src/constants/enums/notification-enum';
import { AccountTypeEnum } from 'src/entities';

export class NotificationItem {
  @ApiProperty({
    enum: NotificationEnum,
    enumName: 'NotificationEnum',
    description: 'Type of notification',
    example: NotificationEnum.TYPE_1,
  })
  notificationType!: NotificationEnum;

  @ApiProperty({
    description: 'Unique identifier of the notification',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  notificationId!: string;

  @ApiProperty({
    description: 'Timestamp when the notification was created',
    example: '2023-12-01T10:30:00Z',
    format: 'date-time',
  })
  createdAt!: string;

  @ApiProperty({
    enum: AccountTypeEnum,
    enumName: 'AccountTypeEnum',
    description: 'Type of account that triggered the notification (page or user)',
    example: AccountTypeEnum.USER,
  })
  accountType!: AccountTypeEnum;

  @ApiProperty({
    description: 'UUID of the account that triggered the notification',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  accountId!: string;

  @ApiProperty({
    description: 'Username of the account that triggered the notification',
    example: 'irem_basoglu',
  })
  accountUsername!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'Avatar URL of the account that triggered the notification',
    example: 'https://example.com/avatar.jpg',
    format: 'url',
  })
  accountAvatarUrl!: string | null;

  @ApiProperty({
    description: 'Type of content related to the notification',
    example: 'vibe',
  })
  contentType!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'UUID of the content related to the notification',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  contentId!: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'Thumbnail URL of the content',
    example: 'https://example.com/thumbnail.jpg',
    format: 'url',
  })
  contentThumbnailUrl!: string | null;

  @ApiProperty({
    description: 'Name or title of the content',
    example: 'Teoman Concert Vibe',
  })
  contentName!: string;

  @ApiProperty({
    description: 'Description of the content item',
    example: '3 Memories',
  })
  contentItem!: string;

  @ApiProperty({
    description: 'Type of action that triggered the notification',
    example: 'memory_added',
  })
  actionType!: string;

  @ApiProperty({
    description: 'UUID of the item to navigate to when notification is tapped',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  actionId!: string;
}
