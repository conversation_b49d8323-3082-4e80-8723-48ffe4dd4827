import { ApiProperty } from '@nestjs/swagger';
import { IsArray, <PERSON>Enum, IsString, IsUUID, IsUrl, Matches } from 'class-validator';
import { UserEduVerificationEnum, UserGenderEnum } from 'src/entities';

export class RegisterDto {
  @ApiProperty({
    example: '+90(500)4003020',
    description: 'Phone number in international format with country code',
  })
  @IsString()
  @Matches(/^\+\d{1,3}\(\d{3}\)\d{7}$/, { message: 'Phone number must be in format +XX(XXX)XXXXXXX' })
  phoneNumber!: string;

  @ApiProperty({
    description: "User's full name can only contain letters and spaces",
    example: '<PERSON>',
    minLength: 2,
    maxLength: 50,
  })
  @IsString()
  @Matches(/^[a-zA-Z ]{2,50}$/, {
    message: 'Full name can only contain letters and spaces (2-50 characters)',
  })
  fullname!: string;

  @ApiProperty({
    format: 'uuid',
    description: 'Array of hobby UUIDs that the user is interested in',
    example: ['123e4567-e89b-12d3-a456-************'],
  })
  @IsArray()
  @IsUUID('4', { each: true, message: 'Each hobby ID must be a valid UUID v4' })
  hobbyIds!: string[];
}

export class GetContactsByUserIdDto {
  @ApiProperty({
    example: ['+90(500)4003020'],
    description: 'Array of phone numbers to check for contacts',
  })
  @IsArray()
  @Matches(/^\+\d{1,3}\(\d{3}\)\d{7}$/, {
    each: true,
    message: 'Each phone number must be in format +XX(XXX)XXXXXXX',
  })
  phoneNumbers!: string[];
}

export class RemoveFollowerByUserIdDto {
  @ApiProperty({
    description: 'UUID of the follower to remove',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsUUID('4', { message: 'followerId must be a valid UUID v4' })
  followerId!: string;
}

export class SendCreatorRequestFormDto {}

export class SendVerificationEmailDto {}

export class UpdateByUserIdDto {
  @ApiProperty({
    description: 'Username can only contain letters, numbers, underscores, and hyphens',
    example: 'john_doe123',
    minLength: 4,
    maxLength: 20,
  })
  @IsString()
  @Matches(/^[a-zA-Z0-9_\-]{4,20}$/, {
    message: 'Username can only contain letters, numbers, underscores, and hyphens (4-20 characters)',
  })
  newUsername!: string;

  @ApiProperty({
    description: 'Birthday in YYYY-MM-DD format',
    example: '1990-01-15',
    format: 'date',
  })
  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'Birthday must be in YYYY-MM-DD format',
  })
  newBirthday!: string;

  @ApiProperty({
    enum: UserGenderEnum,
    enumName: 'UserGenderEnum',
    description: 'Gender of the user',
    example: UserGenderEnum.MALE,
  })
  @IsEnum(UserGenderEnum, { message: 'Gender must be a valid UserGenderEnum value' })
  newGender!: UserGenderEnum;

  @ApiProperty({
    description: "URL to the user's avatar image",
    example: 'https://example.com/avatar.jpg',
    format: 'url',
  })
  @IsUrl({}, { message: 'Avatar URL must be a valid URL' })
  newAvatarUrl!: string;
}

export class UpdateEmailByUserIdDto {
  @ApiProperty({
    description: 'New email address for the user',
    example: '<EMAIL>',
    format: 'email',
  })
  @IsString()
  @Matches(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, { message: 'Please provide a valid email address' })
  newEmail!: string;
}

export class UpdateGradByUserIdDto {
  @ApiProperty({
    enum: UserEduVerificationEnum,
    enumName: 'UserEduVerificationEnum',
    description: 'User education verification status',
    example: UserEduVerificationEnum.STUDENT,
  })
  @IsEnum(UserEduVerificationEnum, {
    message: 'Graduation status must be a valid UserEduVerificationEnum value',
  })
  newGrad!: UserEduVerificationEnum;
}

export class UpdateNotificationsByUserIdDto {}

export class UpdatePhoneNumberByUserIdDto {
  @ApiProperty({
    example: '+90(500)4003020',
    description: 'New phone number in international format with country code',
  })
  @IsString()
  @Matches(/^\+\d{1,3}\(\d{3}\)\d{7}$/, { message: 'Phone number must be in format +XX(XXX)XXXXXXX' })
  newPhoneNumber!: string;
}

export class UpdatePrivacyByUserIdDto {}

export class ValidateEmailDto {}
