class SearchBoxCategoryListReturn {
  final List<SearchBoxCategoryListReturn_p_list_items> list_items;
  final String attribution;
  final String version;

  SearchBoxCategoryListReturn({
    required this.list_items,
    required this.attribution,
    required this.version,
  });

  factory SearchBoxCategoryListReturn.fromJson(json) {
    return SearchBoxCategoryListReturn(
      list_items: json['list_items']
          .map<SearchBoxCategoryListReturn_p_list_items>(
              (val) => SearchBoxCategoryListReturn_p_list_items.from<PERSON>son(val))
          .toList(),
      attribution: json['attribution'],
      version: json['version'],
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'list_items': list_items.map((SearchBoxCategoryListReturn_p_list_items val) => val.toJson()).toList(),
      'attribution': attribution,
      'version': version,
    };
    return jsonObject;
  }
}

class SearchBoxCategoryListReturn_p_list_items {
  final String canonical_id;
  final String icon;
  final String name;

  SearchBoxCategoryListReturn_p_list_items({
    required this.canonical_id,
    required this.icon,
    required this.name,
  });

  factory SearchBoxCategoryListReturn_p_list_items.fromJson(json) {
    return SearchBoxCategoryListReturn_p_list_items(
      canonical_id: json['canonical_id'],
      icon: json['icon'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'canonical_id': canonical_id,
      'icon': icon,
      'name': name,
    };
    return jsonObject;
  }
}
