class SearchBoxForwardReturn {
  final String type;
  final List<SearchBoxForwardReturn_p_features> features;
  final String attribution;

  SearchBoxForwardReturn({
    required this.type,
    required this.features,
    required this.attribution,
  });

  factory SearchBoxForwardReturn.fromJson(json) {
    return SearchBoxForwardReturn(
      type: json['type'],
      features: json['features']
          .map<SearchBoxForwardReturn_p_features>((val) => SearchBoxForwardReturn_p_features.fromJson(val))
          .toList(),
      attribution: json['attribution'],
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'type': type,
      'features': features.map((SearchBoxForwardReturn_p_features val) => val.toJson()).toList(),
      'attribution': attribution,
    };
    return jsonObject;
  }
}

class SearchBoxForwardReturn_p_features {
  final String type;
  final SearchBoxGeometry geometry;
  final SearchBoxProperties properties;

  SearchBoxForwardReturn_p_features({
    required this.type,
    required this.geometry,
    required this.properties,
  });

  factory SearchBoxForwardReturn_p_features.fromJson(json) {
    return SearchBoxForwardReturn_p_features(
      type: json['type'],
      geometry: SearchBoxGeometry.fromJson(json['geometry']),
      properties: SearchBoxProperties.fromJson(json['properties']),
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'type': type,
      'geometry': geometry.toJson(),
      'properties': properties.toJson(),
    };
    return jsonObject;
  }
}
