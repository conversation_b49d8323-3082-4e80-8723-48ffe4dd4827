import { Body, Controller, DefaultValuePipe, Get, ParseIntPipe, Post, Query, Res } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { HobbiesSearchOriginEnum } from 'src/constants/enums/hobbies-search-origin-enum';
import { HobbiesService } from './hobbies.service';
import { AddHobbiesByHobbyIdDto } from './models/hobbies.dto';
import { SearchHobbiesReturn } from './models/hobbies.returns';

@ApiTags('hobbies')
@Controller('hobbies')
export class HobbiesController {
  constructor(private readonly hobbiesService: HobbiesService) {}

  @ApiOperation({
    summary: 'Seçilebilecek hobileri listeler',
  })
  @ApiResponse({
    status: 200,
    type: SearchHobbiesReturn,
  })
  @ApiQuery({
    name: 'type',
    enum: HobbiesSearchOriginEnum,
    enumName: 'HobbiesSearchOriginEnum',
    description: 'Type of search to perform - either for the profile or for the default search',
    example: HobbiesSearchOriginEnum.DEFAULT,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get('search')
  async searchHobbies(
    @Res({ passthrough: true }) res: Response,
    @Query('type') type: HobbiesSearchOriginEnum,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.hobbiesService.searchHobbies({
      type,
      sessionId,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Yeni hobiler ekler',
  })
  @ApiResponse({ status: 200 })
  @Post('add')
  async addHobbiesByHobbyId(
    @Body() addHobbiesByHobbyIdDto: AddHobbiesByHobbyIdDto,
    @Res({ passthrough: true }) res: Response,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.hobbiesService.addHobbiesByHobbyId({
      sessionId,
      sessionRole,
      ...addHobbiesByHobbyIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
}
