import { ApiProperty } from '@nestjs/swagger';

export class CommentItem {
  @ApiProperty({
    format: 'uuid',
    description: 'UUID of the comment',
  })
  commentId!: string;

  @ApiProperty({
    description: 'Text content of the comment',
  })
  comment!: string;

  @ApiProperty({
    format: 'uuid',
    description: 'UUID of the user who made the comment',
  })
  commenterUserId!: string;

  @ApiProperty({
    description: 'Username of the user who made the comment',
    example: 'john_doe',
  })
  commenterUsername!: string;

  @ApiProperty({
    format: 'date-time',
    description: 'Timestamp when the comment was created',
  })
  createdAt!: string;
}
