import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  AccountTypeEnum,
  Ivent,
  IventCollab,
  IventCollabStatusEnum,
  IventCreatorTypeEnum,
  IventDate,
  IventPrivacyEnum,
  IventTag,
  IventUniversity,
  IventViewTypeEnum,
  Location,
  MemoryFolder,
  VibeFolder,
} from 'src/entities';
import { EmptyReturn } from 'src/models/empty-return';
import { hydrateComputedList } from 'src/utils/hydrate-computed-list';
import { insertQueryBuilder } from 'src/utils/insert-query-builder';
import { stringToBooleanTransformer, stringToNumberTransformer } from 'src/utils/value-transformers';
import { DataSource, Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { FirebaseStorageService } from '../firebase/firebase-storage.service';
import {
  CreateIventParams,
  DeleteIventByIventIdParams,
  FavoriteIventByIventIdParams,
  GetBannerByIventIdParams,
  GetIventPageByIventIdParams,
  GetLatestIventsParams,
  GetSuggestedImagesParams,
  GetUpcomingIventParams,
  UnfavoriteIventByIventIdParams,
  UpdateDateByIventIdParams,
  UpdateDetailsByIventIdParams,
  UpdateLocationByIventIdParams,
} from './models/ivents.params';
import {
  CreateIventReturn,
  GetBannerByIventIdReturn,
  GetIventPageByIventIdReturn,
  GetLatestIventsReturn,
  GetSuggestedImagesReturn,
  GetUpcomingIventReturn,
} from './models/ivents.returns';

@Injectable()
export class IventsService {
  constructor(
    private dataSource: DataSource,
    private firebaseStorage: FirebaseStorageService,
    @InjectRepository(Ivent)
    private iventRepository: Repository<Ivent>,
    @InjectRepository(Location)
    private locationRepository: Repository<Location>,
  ) {}

  async createIvent(createIventParams: CreateIventParams): Promise<CreateIventReturn> {
    const {
      sessionId,
      sessionRole,
      creatorType,
      iventName,
      thumbnailUrl,
      thumbnailBuffer,
      dates,
      mapboxId,
      latitude,
      longitude,
      description,
      categoryTagId,
      tagIds,
      privacy,
      allowedUniversityCodes,
      collabs,
      googleFormsUrl,
      callNumber,
      instagramUsername,
      websiteUrl,
      whatsappUrl,
      whatsappNumber,
      isWhatsappUrlPrivate,
    } = createIventParams;

    var mediaUrl;
    if (thumbnailBuffer) {
      const thumbnailId = uuidv4();
      mediaUrl = await this.firebaseStorage.uploadImage(thumbnailBuffer, thumbnailId, 'ivents');
    } else if (thumbnailUrl) {
      mediaUrl = thumbnailUrl;
    }

    const newIvent = new Ivent();
    newIvent.creator_user_id = sessionId;
    newIvent.creator_type = IventCreatorTypeEnum.USER;
    newIvent.ivent_name = iventName;
    newIvent.thumbnail_url = mediaUrl;
    newIvent.description = description || null;
    newIvent.category_tag_id = categoryTagId;
    newIvent.privacy = privacy;
    newIvent.google_forms_url = googleFormsUrl || null;
    newIvent.call_number = callNumber || null;
    newIvent.website_url = websiteUrl || null;
    newIvent.instagram_username = instagramUsername || null;
    newIvent.whatsapp_url = whatsappUrl || null;
    newIvent.whatsapp_number = whatsappNumber || null;
    newIvent.is_whatsapp_url_private = isWhatsappUrlPrivate || false;

    const location = await this.locationRepository.findOneBy({ mapbox_id: mapboxId });
    newIvent.location = location!;

    newIvent.vibe_folder = new VibeFolder();
    newIvent.memory_folder = new MemoryFolder();

    newIvent.tags = tagIds.map((val) => {
      const newIventTag = new IventTag();
      newIventTag.hobby_id = val;
      return newIventTag;
    });

    newIvent.dates = dates.map((val) => {
      const newIventDate = new IventDate();
      newIventDate.ivent_date = new Date(val);
      return newIventDate;
    });

    if (privacy === IventPrivacyEnum.SELECTED_EDU)
      newIvent.universities = allowedUniversityCodes.map((val) => {
        const newIventUniversity = new IventUniversity();
        newIventUniversity.university_code = val;
        return newIventUniversity;
      });

    newIvent.collabs = [...collabs, { id: sessionId, type: AccountTypeEnum.USER }].map((val) => {
      const newIventCollab = new IventCollab();
      newIventCollab.collab_user_id = val.type === AccountTypeEnum.USER ? val.id : null;
      newIventCollab.collab_page_id = val.type === AccountTypeEnum.PAGE ? val.id : null;
      newIventCollab.collab_type = val.type;
      newIventCollab.inviter_id = sessionId;
      newIventCollab.status = val.id === sessionId ? IventCollabStatusEnum.ADMIN : IventCollabStatusEnum.PENDING;
      return newIventCollab;
    });

    const insertedIvent = await this.iventRepository.save(newIvent);

    return {
      iventId: insertedIvent.id,
    };
  }

  async deleteIventByIventId(deleteIventByIventIdParams: DeleteIventByIventIdParams): Promise<EmptyReturn> {
    const { ...rest } = deleteIventByIventIdParams;
    return {};
  }

  async getIventPageByIventId(
    getIventPageByIventIdParams: GetIventPageByIventIdParams,
  ): Promise<GetIventPageByIventIdReturn> {
    const { sessionId, sessionRole, iventId } = getIventPageByIventIdParams;

    const { entities, raw } = await this.dataSource
      .getRepository(Ivent)
      .createQueryBuilder('i')
      .leftJoinAndSelect('i.location', 'l')
      .leftJoinAndSelect('i.creator_page', 'p')
      .leftJoinAndSelect('i.creator_user', 'u')
      .leftJoinAndSelect('i.creator_distributor', 'dis')
      .leftJoinAndSelect('i.category_tag', 'h')
      .leftJoinAndSelect('i.ivent_dates_aggregated', 'd')
      .leftJoinAndSelect('i.ivent_tags_aggregated', 'ita')
      .leftJoinAndSelect('i.active_session_ivents', 'asi', 'asi.account_id = :sessionId', { sessionId })
      .leftJoinAndSelect('i.collab_summary_of_ivent', 'csof', 'csof.account_id = :sessionId', { sessionId })
      .leftJoinAndSelect('i.member_summary_of_session_squad', 'msoss', 'msoss.user_id = :sessionId', { sessionId })
      .leftJoinAndSelect('i.participant_summary_of_ivent', 'psoi')
      .leftJoinAndSelect('i.session_friend_summary_of_ivent', 'sfsoi', 'sfsoi.user_id = :sessionId', { sessionId })
      .leftJoin(
        (qb) =>
          qb
            .select('uf.favorited_ivent_id', 'ivent_id')
            .addSelect('COUNT(uf.user_id)', 'favorite_count')
            .from('user_favorites', 'uf')
            .groupBy('uf.favorited_ivent_id'),
        'fcoi',
        'fcoi.ivent_id = i.id',
      )
      .leftJoin('user_favorites', 'ufa', 'ufa.favorited_ivent_id = i.id AND ufa.user_id = :sessionId', { sessionId })
      .addSelect('fcoi.favorite_count', 'favorite_count')
      .addSelect('CASE WHEN ufa.user_id IS NOT NULL THEN TRUE ELSE FALSE END', 'is_favorited')
      .where('i.id = :iventId', { iventId })
      .getRawAndEntities();

    const ivent = hydrateComputedList(entities, raw, [
      { field: 'favorite_count', transformer: stringToNumberTransformer },
      { field: 'is_favorited', transformer: stringToBooleanTransformer },
    ])[0]!;

    var collabCount = ivent.collab_count;
    var collabNames = ivent.collab_names;
    if (collabNames.includes(ivent.creator_name!)) {
      collabCount -= 1;
      collabNames = collabNames.filter((val) => val !== ivent.creator_name!);
    } else {
      collabNames.pop();
    }

    var isFavorited;
    var favoriteCount;
    var memberNames;
    switch (ivent.view_type) {
      case IventViewTypeEnum.CREATED:
        isFavorited = ivent.is_favorited!;
        favoriteCount = ivent.favorite_count!;
        memberNames = null;
        break;
      case IventViewTypeEnum.JOINED:
        isFavorited = false;
        favoriteCount = ivent.favorite_count!;
        memberNames = null;
        break;
      case IventViewTypeEnum.DEFAULT:
        isFavorited = ivent.is_favorited!;
        favoriteCount = null;
        memberNames = ivent.member_names!;
        break;
    }

    return {
      iventId: ivent.id,
      iventName: ivent.ivent_name,
      thumbnailUrl: ivent.thumbnail_url,
      locationName: ivent.location!.location_name,
      locationId: ivent.location!.id,
      mapboxId: ivent.location!.mapbox_id,
      creatorId: ivent.creator_id!,
      creatorType: ivent.creator_type!,
      creatorUsername: ivent.creator_name!,
      creatorImageUrl: ivent.creator_image_url,
      description: ivent.description,
      categoryTag: ivent.category_tag!.hobby_name,
      tagNames: ivent.tag_list,
      dates: ivent.date_list,
      googleFormsUrl: ivent.google_forms_url,
      callNumber: ivent.call_number,
      instagramUsername: ivent.instagram_username,
      websiteUrl: ivent.website_url,
      whatsappUrl: ivent.whatsapp_url,
      whatsappNumber: ivent.whatsapp_number,
      isWhatsappUrlPrivate: ivent.is_whatsapp_url_private,
      viewType: ivent.view_type!,
      collabCount,
      collabNames,
      memberCount: ivent.member_count!,
      memberAvatarUrls: ivent.member_avatar_urls!,
      memberFirstnames: memberNames,
      isFavorited,
      favoriteCount,
    };
  }

  async getBannerByIventId(getBannerByIventIdParams: GetBannerByIventIdParams): Promise<GetBannerByIventIdReturn> {
    const { sessionId, sessionRole, iventIds } = getBannerByIventIdParams;

    const query = sessionId
      ? `
      SELECT
          i.id AS ivent_id,
          i.ivent_name AS ivent_name,
          i.thumbnail_url AS thumbnail_url,
          l.location_name AS location_name,
          i.creator_type AS creator_type,
          COALESCE(p.id, u.id, dis.id) AS creator_id,
          COALESCE(p.page_name, u.username, dis.distributor_name) AS creator_name,
          COALESCE(p.thumbnail_url, u.avatar_url, dis.thumbnail_url) AS creator_image_url,
          'default' AS view_type,
          EXISTS (
              SELECT 1
              FROM user_favorites
              WHERE
                  user_id = '${sessionId}'
                  AND favorited_ivent_id = i.id
          ) AS is_favorited
      FROM ivents i
      LEFT JOIN locations l ON l.id = i.location_id
      LEFT JOIN pages p ON p.id = i.creator_page_id
      LEFT JOIN users u ON u.id = i.creator_user_id
      LEFT JOIN distributors dis ON dis.id = i.creator_distributor_id
      WHERE i.id IN (${iventIds.map((id) => `'${id}'`).join(', ')});
    `
      : `
      SELECT
          i.id AS ivent_id,
          i.ivent_name AS ivent_name,
          i.thumbnail_url AS thumbnail_url,
          l.location_name AS location_name,
          i.creator_type AS creator_type,
          COALESCE(p.id, u.id, dis.id) AS creator_id,
          COALESCE(p.page_name, u.username, dis.distributor_name) AS creator_name,
          COALESCE(p.thumbnail_url, u.avatar_url, dis.thumbnail_url) AS creator_image_url,
          'viewer' AS view_type
      FROM ivents i
      LEFT JOIN locations l ON l.id = i.location_id
      LEFT JOIN pages p ON p.id = i.creator_page_id
      LEFT JOIN users u ON u.id = i.creator_user_id
      LEFT JOIN distributors dis ON dis.id = i.creator_distributor_id
      WHERE i.id IN (${iventIds.map((id) => `'${id}'`).join(', ')});
    `;

    const queryResult = await this.dataSource.query(query);

    if (queryResult.length === 0) {
      throw new HttpException('Ivent not found.', HttpStatus.NOT_FOUND);
    }

    return {
      ivents: queryResult.map((val) => ({
        iventId: val.ivent_id,
        iventName: val.ivent_name,
        thumbnailUrl: val.thumbnail_url,
        locationName: val.location_name,
        creatorId: val.creator_id,
        creatorType: val.creator_type,
        creatorName: val.creator_name,
        creatorImageUrl: val.creator_image_url,
        viewType: val.view_type,
        isFavorited: val.is_favorited ? true : false,
      })),
      iventCount: queryResult.length,
    };
  }

  async getLatestIvents(getLatestIventsParams: GetLatestIventsParams): Promise<GetLatestIventsReturn> {
    const { sessionId, sessionRole, limit, page } = getLatestIventsParams;

    if (sessionId ?? true) {
      throw new HttpException('You must login to see this content.', HttpStatus.BAD_REQUEST);
    }

    const result = await this.dataSource.query(`
      SELECT
          i.id AS ivent_id,
          i.ivent_name AS ivent_name,
          i.thumbnail_url AS thumbnail_url,
          d.date AS date,
          l.location_name AS location_name,
          i.creator_type AS creator_type,
          COALESCE(p.id, u.id, dis.id) AS creator_id,
          COALESCE(p.page_name, u.username, dis.distributor_name) AS creator_name,
          COALESCE(p.thumbnail_url, u.avatar_url, dis.thumbnail_url) AS creator_image_url,
          asi.view_type AS view_type,
          CASE
              WHEN asi.view_type = 'joined' THEN msoss.member_summary
              WHEN asi.view_type = 'created' THEN csof.collab_summary
          END AS member_summary
      FROM ivent_users iu
      LEFT JOIN ivents i ON i.id = iu.ivent_id
      LEFT JOIN locations l ON l.id = i.location_id
      LEFT JOIN pages p ON p.id = i.creator_page_id
      LEFT JOIN users u ON u.id = i.creator_user_id
      LEFT JOIN distributors dis ON dis.id = i.creator_distributor_id
      LEFT JOIN ivent_dates_aggregated d ON d.ivent_id = i.id
      LEFT JOIN active_session_ivents asi ON asi.ivent_id = i.id
      LEFT JOIN member_summary_of_session_squad msoss ON msoss.ivent_id = i.id
      LEFT JOIN collab_summary_of_ivent csof ON csof.ivent_id = i.id
      WHERE iu.account_id = '${sessionId}'
      AND asi.account_id = '${sessionId}'
      AND msoss.user_id = '${sessionId}'
      AND csof.account_id = '${sessionId}'
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    return {
      ivents: result.map((val) => ({
        iventId: val.ivent_id,
        iventName: val.ivent_name,
        thumbnailUrl: val.thumbnail_url,
        locationName: val.location_name,
        date: val.date,
        creatorId: val.creator_id,
        creatorType: val.creator_type,
        creatorName: result[0].creator_name,
        creatorImageUrl: result[0].creator_image_url,
        viewType: val.view_type,
        memberAvatarUrls: val.member_summary ? val.member_summary.split('|')[0].split(',') : [],
        memberCount: val.member_summary ? Number(val.member_summary.split('|')[1]) : 0,
        memberNames: val.member_summary ? val.member_summary.split('|')[2].split(',') : [],
        squadId: val.member_summary && val.view_type === 'joined' ? val.member_summary.split('|')[3] : null,
      })),
      iventCount: result.length,
    };
  }

  async getSuggestedImages(getSuggestedImagesParams: GetSuggestedImagesParams): Promise<GetSuggestedImagesReturn> {
    const { ...rest } = getSuggestedImagesParams;
    return {
      imageUrls: [],
      imageCount: 0,
    };
  }

  async updateDateByIventId(
    // TODO: birden fazla date veya tek date? karmaşasını çöz, tek date olacaksa get ivent details kısmını tamamla
    updateDateByIventIdParams: UpdateDateByIventIdParams,
  ): Promise<EmptyReturn> {
    const { sessionId, sessionRole, iventId, newDates } = updateDateByIventIdParams;
    return {};
  }

  async updateDetailsByIventId(
    // TODO: details olarak nelerin güncellendiğini bul ve kodu düzenle
    updateDetailsByIventIdParams: UpdateDetailsByIventIdParams,
  ): Promise<EmptyReturn> {
    const { sessionId, sessionRole, iventId, newDescription } = updateDetailsByIventIdParams;
    await this.dataSource
      .createQueryBuilder()
      .update('ivents')
      .set({
        description: newDescription,
      })
      .where({
        id: iventId,
      })
      .execute();
    return {};
  }

  async updateLocationByIventId(updateLocationByIventIdParams: UpdateLocationByIventIdParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, iventId, newlocationId } = updateLocationByIventIdParams;
    await this.dataSource
      .createQueryBuilder()
      .update('ivents')
      .set({
        location_id: newlocationId,
      })
      .where({
        id: iventId,
      })
      .execute();
    return {};
  }

  async favoriteIventByIventId(favoriteIventByIventIdParams: FavoriteIventByIventIdParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, iventId } = favoriteIventByIventIdParams;

    await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'user_favorites',
        values: {
          user_id: sessionId,
          favorited_ivent_id: iventId,
        },
      }),
    );

    return {};
  }

  async unfavoriteIventByIventId(unfavoriteIventByIventIdParams: UnfavoriteIventByIventIdParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, iventId } = unfavoriteIventByIventIdParams;

    await this.dataSource
      .createQueryBuilder()
      .delete()
      .from('user_favorites')
      .where({
        user_id: sessionId,
        favorited_ivent_id: iventId,
      })
      .execute();

    return {};
  }

  async getUpcomingIvent(getUpcomingIventParams: GetUpcomingIventParams): Promise<GetUpcomingIventReturn> {
    const { sessionId, sessionRole } = getUpcomingIventParams;

    const query = `
      SELECT
          i.id AS ivent_id,
          i.ivent_name AS ivent_name,
          id.date AS date,
          asi.view_type AS view_type,
          CASE
              WHEN asi.view_type = 'joined' THEN msoss.member_summary
              WHEN asi.view_type = 'created' THEN csof.collab_summary
          END AS member_summary
      FROM ivents i
      LEFT JOIN ivent_dates id ON id.ivent_id = i.id
      LEFT JOIN active_session_ivents asi ON asi.ivent_id = i.id
      LEFT JOIN member_summary_of_session_squad msoss ON msoss.ivent_id = i.id
      LEFT JOIN collab_summary_of_ivent csof ON csof.ivent_id = i.id
      WHERE id.date >= NOW()
      AND id.date <= NOW() + INTERVAL '1 day'
      AND asi.view_type IS NOT NULL
      AND asi.account_id = $1
      AND msoss.user_id = $1
      AND csof.account_id = $1
      ORDER BY id.date ASC
      LIMIT 1;
    `;
    const queryResult = await this.dataSource.query(query, [sessionId]);
    if (queryResult.length === 0) {
      throw new HttpException('No upcoming ivent found.', HttpStatus.NOT_FOUND);
    }

    return {
      iventId: queryResult[0].ivent_id,
      iventName: queryResult[0].ivent_name,
      dates: queryResult[0].date,
      memberCount: queryResult[0].member_summary ? Number(queryResult[0].member_summary.split('|')[0]) : 0,
      memberFirstnames: queryResult[0].member_summary ? queryResult[0].member_summary.split('|')[1].split(',') : [],
    };
  }
}
