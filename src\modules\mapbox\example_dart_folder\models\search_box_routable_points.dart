class SearchBoxRoutablePoints {
  final String name;
  final double latitude;
  final double longitude;

  SearchBoxRoutablePoints({
    required this.name,
    required this.latitude,
    required this.longitude,
  });

  factory SearchBoxRoutablePoints.fromJson(json) {
    return SearchBoxRoutablePoints(
      name: json['name'],
      latitude: json['latitude'],
      longitude: json['longitude'],
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'name': name,
      'latitude': latitude,
      'longitude': longitude,
    };
    return jsonObject;
  }
}
