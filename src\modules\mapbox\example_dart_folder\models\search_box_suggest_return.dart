class SearchBoxSuggestReturn {
  final List<SearchBoxSuggestReturn_p_suggestions> suggestions;
  final String attribution;

  SearchBoxSuggestReturn({
    required this.suggestions,
    required this.attribution,
  });

  factory SearchBoxSuggestReturn.fromJson(json) {
    return SearchBoxSuggestReturn(
      suggestions: json['suggestions']
          .map<SearchBoxSuggestReturn_p_suggestions>((val) => SearchBoxSuggestReturn_p_suggestions.from<PERSON>son(val))
          .toList(),
      attribution: json['attribution'],
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'suggestions': suggestions.map((SearchBoxSuggestReturn_p_suggestions val) => val.toJson()).toList(),
      'attribution': attribution,
    };
    return jsonObject;
  }
}

class SearchBoxSuggestReturn_p_suggestions {
  final String name;
  final String? name_preferred;
  final String mapbox_id;
  final String feature_type;
  final String? address;
  final String? full_address;
  final String place_formatted;
  final SearchBoxContext context;
  final String language;
  final String? maki;
  final List<String>? poi_category;
  final List<String>? poi_category_ids;
  final List<String>? brand;
  final List<String>? brand_id;
  final Map<String, String>? external_ids;
  final Map<String, dynamic>? metadata;
  final int? distance;
  final double? eta;
  final double? added_distance;
  final double? added_time;

  SearchBoxSuggestReturn_p_suggestions({
    required this.name,
    required this.name_preferred,
    required this.mapbox_id,
    required this.feature_type,
    required this.address,
    required this.full_address,
    required this.place_formatted,
    required this.context,
    required this.language,
    required this.maki,
    required this.poi_category,
    required this.poi_category_ids,
    required this.brand,
    required this.brand_id,
    required this.external_ids,
    required this.metadata,
    required this.distance,
    required this.eta,
    required this.added_distance,
    required this.added_time,
  });

  factory SearchBoxSuggestReturn_p_suggestions.fromJson(json) {
    return SearchBoxSuggestReturn_p_suggestions(
      name: json['name'],
      name_preferred: json['name_preferred'],
      mapbox_id: json['mapbox_id'],
      feature_type: json['feature_type'],
      address: json['address'],
      full_address: json['full_address'],
      place_formatted: json['place_formatted'],
      context: SearchBoxContext.fromJson(json['context']),
      language: json['language'],
      maki: json['maki'],
      poi_category: json['poi_category'] != null ? List<String>.from(json['poi_category']) : null,
      poi_category_ids: json['poi_category_ids'] != null ? List<String>.from(json['poi_category_ids']) : null,
      brand: json['brand'] != null ? List<String>.from(json['brand']) : null,
      brand_id: json['brand_id'] != null ? List<String>.from(json['brand_id']) : null,
      external_ids: json['external_ids'] != null ? Map<String, String>.from(json['external_ids']) : null,
      metadata: json['metadata'] != null ? Map<String, dynamic>.from(json['metadata']) : null,
      distance: json['distance'],
      eta: json['eta'],
      added_distance: json['added_distance'],
      added_time: json['added_time'],
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'name': name,
      'name_preferred': name_preferred,
      'mapbox_id': mapbox_id,
      'feature_type': feature_type,
      'address': address,
      'full_address': full_address,
      'place_formatted': place_formatted,
      'context': context.toJson(),
      'language': language,
      'maki': maki,
      'poi_category': poi_category,
      'poi_category_ids': poi_category_ids,
      'brand': brand,
      'brand_id': brand_id,
      'external_ids': external_ids,
      'metadata': metadata,
      'distance': distance,
      'eta': eta,
      'added_distance': added_distance,
      'added_time': added_time,
    };
    return jsonObject;
  }
}
