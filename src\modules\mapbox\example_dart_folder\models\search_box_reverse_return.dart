class SearchBoxReverseReturn {
  final String type;
  final List<SearchBoxReverseReturn_p_features> features;
  final String attribution;

  SearchBoxReverseReturn({
    required this.type,
    required this.features,
    required this.attribution,
  });

  factory SearchBoxReverseReturn.fromJson(json) {
    return SearchBoxReverseReturn(
      type: json['type'],
      features: json['features']
          .map<SearchBoxReverseReturn_p_features>((val) => SearchBoxReverseReturn_p_features.fromJson(val))
          .toList(),
      attribution: json['attribution'],
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'type': type,
      'features': features.map((SearchBoxReverseReturn_p_features val) => val.toJson()).toList(),
      'attribution': attribution,
    };
    return jsonObject;
  }
}

class SearchBoxReverseReturn_p_features {
  final String type;
  final SearchBoxGeometry geometry;
  final SearchBoxProperties properties;

  SearchBoxReverseReturn_p_features({
    required this.type,
    required this.geometry,
    required this.properties,
  });

  factory SearchBoxReverseReturn_p_features.fromJson(json) {
    return SearchBoxReverseReturn_p_features(
      type: json['type'],
      geometry: SearchBoxGeometry.fromJson(json['geometry']),
      properties: SearchBoxProperties.fromJson(json['properties']),
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'type': type,
      'geometry': geometry.toJson(),
      'properties': properties.toJson(),
    };
    return jsonObject;
  }
}
