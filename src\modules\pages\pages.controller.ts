import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import {
  CreatePageDto,
  RemoveFollowerByPageIdDto,
  UpdateDescriptionByPageIdDto,
  UpdateLinksByPageIdDto,
  UpdateLocationByPageIdDto,
} from './models/pages.dto';
import {
  CreatePageReturn,
  GetIventsCreatedByPageIdReturn,
  GetPageByPageIdReturn,
  GetPageDetailsByPageIdReturn,
  GetVibeFoldersByPageIdReturn,
  SearchFollowersByPageIdReturn,
} from './models/pages.returns';
import { PagesService } from './pages.service';

@ApiTags('pages')
@Controller('pages')
export class PagesController {
  constructor(private readonly pagesService: PagesService) {}

  @ApiOperation({
    summary: 'Sayfa oluşturur',
  })
  @ApiResponse({
    status: 200,
    type: CreatePageReturn,
  })
  @Post('create')
  async createPage(@Body() createPageDto: CreatePageDto, @Res({ passthrough: true }) res: Response) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.pagesService.createPage({
      sessionId,
      sessionRole,
      ...createPageDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile sayfayı siler',
  })
  @ApiResponse({ status: 200 })
  @Delete(':id/delete')
  async deletePageByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.pagesService.deletePageByPageId({
      sessionId,
      sessionRole,
      pageId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile sayfanın bilgilerini listeler',
  })
  @ApiResponse({
    status: 200,
    type: GetPageByPageIdReturn,
  })
  @Get(':id')
  async getPageByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.pagesService.getPageByPageId({
      sessionId,
      sessionRole,
      pageId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile sayfanın iventlerini listeler',
  })
  @ApiResponse({
    status: 200,
    type: GetIventsCreatedByPageIdReturn,
  })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':id/ivents')
  async getIventsCreatedByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) pageId: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.pagesService.getIventsCreatedByPageId({
      sessionId,
      sessionRole,
      pageId,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile sayfanın vibelarını listeler',
  })
  @ApiResponse({
    status: 200,
    type: GetVibeFoldersByPageIdReturn,
  })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':id/vibes')
  async getVibesByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) pageId: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.pagesService.getVibeFoldersByPageId({
      sessionId,
      sessionRole,
      pageId,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile sayfanın detaylarını listeler',
  })
  @ApiResponse({
    status: 200,
    type: GetPageDetailsByPageIdReturn,
  })
  @Get(':id/details')
  async getDetailsByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.pagesService.getDetailsByPageId({
      sessionId,
      sessionRole,
      pageId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile sayfanın açıklamasını günceller',
  })
  @ApiResponse({ status: 200 })
  @Put(':id/update/description')
  async updateDescriptionByPageId(
    @Body() updateDescriptionByPageIdDto: UpdateDescriptionByPageIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.pagesService.updateDescriptionByPageId({
      sessionId,
      sessionRole,
      pageId,
      ...updateDescriptionByPageIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile sayfanın linklerini günceller',
  })
  @ApiResponse({ status: 200 })
  @Put(':id/update/links')
  async updateLinksByPageId(
    @Body() updateLinksByPageIdDto: UpdateLinksByPageIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.pagesService.updateLinksByPageId({
      sessionId,
      sessionRole,
      pageId,
      ...updateLinksByPageIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile sayfanın konumunu günceller',
  })
  @ApiResponse({ status: 200 })
  @Put(':id/update/location')
  async updateLocationByPageId(
    @Body() updateLocationByPageIdDto: UpdateLocationByPageIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.pagesService.updateLocationByPageId({
      sessionId,
      sessionRole,
      pageId,
      ...updateLocationByPageIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile sayfa engellenir',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/block')
  async blockPageByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.pagesService.blockPageByPageId({
      sessionId,
      sessionRole,
      pageId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile sayfa engeli kaldırılır',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/unblock')
  async unblockPageByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.pagesService.unblockPageByPageId({
      sessionId,
      sessionRole,
      pageId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile etkinlik bildirimlerini açar',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/subscribe')
  async subscribeByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.pagesService.subscribeByPageId({
      sessionId,
      sessionRole,
      pageId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile etkinlik bildirimlerini kapatır',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/unsubscribe')
  async unsubscribeByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.pagesService.unsubscribeByPageId({
      sessionId,
      sessionRole,
      pageId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile takipçileri listeler',
  })
  @ApiResponse({
    status: 200,
    type: SearchFollowersByPageIdReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':id/followers')
  async searchFollowersByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) pageId: string,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.pagesService.searchFollowersByPageId({
      sessionId,
      sessionRole,
      pageId,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile takip edilir',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/follow')
  async followByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.pagesService.followByPageId({
      sessionId,
      sessionRole,
      pageId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile takipten çıkılır',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/unfollow')
  async unfollowByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.pagesService.unfollowByPageId({
      sessionId,
      sessionRole,
      pageId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile takipçi çıkartılır',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/followers/remove')
  async removeFollowerByPageId(
    @Body() removeFollowerByPageIdDto: RemoveFollowerByPageIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.pagesService.removeFollowerByPageId({
      sessionId,
      sessionRole,
      pageId,
      ...removeFollowerByPageIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
}
