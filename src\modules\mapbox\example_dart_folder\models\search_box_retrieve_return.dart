class SearchBoxRetrieveReturn {
  final List<SearchBoxRetrieveReturn_p_features> features;
  final String attribution;

  SearchBoxRetrieveReturn({
    required this.features,
    required this.attribution,
  });

  factory SearchBoxRetrieveReturn.fromJson(json) {
    return SearchBoxRetrieveReturn(
      features: json['features']
          .map<SearchBoxRetrieveReturn_p_features>((val) => SearchBoxRetrieveReturn_p_features.fromJson(val))
          .toList(),
      attribution: json['attribution'],
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'features': features.map((SearchBoxRetrieveReturn_p_features val) => val.toJson()).toList(),
      'attribution': attribution,
    };
    return jsonObject;
  }
}

class SearchBoxRetrieveReturn_p_features {
  final String type;
  final SearchBoxGeometry geometry;
  final SearchBoxProperties properties;

  SearchBoxRetrieveReturn_p_features({
    required this.type,
    required this.geometry,
    required this.properties,
  });

  factory SearchBoxRetrieveReturn_p_features.from<PERSON><PERSON>(json) {
    return SearchBoxRetrieveReturn_p_features(
      type: json['type'],
      geometry: SearchBoxGeometry.from<PERSON>son(json['geometry']),
      properties: SearchBoxProperties.fromJson(json['properties']),
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'type': type,
      'geometry': geometry.toJson(),
      'properties': properties.toJson(),
    };
    return jsonObject;
  }
}
