import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID } from 'class-validator';

export class AddPageMembersByPageIdDto {
  @ApiProperty({
    format: 'uuid',
    description: 'Array of user UUIDs to add as page members',
    example: ['123e4567-e89b-12d3-a456-************'],
  })
  @IsArray()
  @IsUUID('4', { each: true, message: 'Each user ID must be a valid UUID v4' })
  userIds!: string[];
}

export class RemovePageModeratorByPageIdDto {
  @ApiProperty({
    description: 'UUID of the moderator to be removed',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsUUID('4', { message: 'userId must be a valid UUID v4' })
  userId!: string;
}

export class RemovePageMemberByPageIdDto {
  @ApiProperty({
    description: 'UUID of the member to be removed',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsUUID('4', { message: 'userId must be a valid UUID v4' })
  userId!: string;
}

export class TransferPageAdministrationByPageIdDto {
  @ApiProperty({
    description: 'UUID of the user to transfer page administration to',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsUUID('4', { message: 'userId must be a valid UUID v4' })
  userId!: string;
}
