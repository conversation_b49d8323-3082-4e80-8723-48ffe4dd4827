class SearchBoxCategoryReturn {
  final String type;
  final List<SearchBoxCategoryReturn_p_features> features;
  final String attribution;

  SearchBoxCategoryReturn({
    required this.type,
    required this.features,
    required this.attribution,
  });

  factory SearchBoxCategoryReturn.fromJson(json) {
    return SearchBoxCategoryReturn(
      type: json['type'],
      features: json['features']
          .map<SearchBoxCategoryReturn_p_features>((val) => SearchBoxCategoryReturn_p_features.fromJson(val))
          .toList(),
      attribution: json['attribution'],
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'type': type,
      'features': features.map((SearchBoxCategoryReturn_p_features val) => val.toJson()).toList(),
      'attribution': attribution,
    };
    return jsonObject;
  }
}

class SearchBoxCategoryReturn_p_features {
  final String type;
  final SearchBoxGeometry geometry;
  final SearchBoxProperties properties;

  SearchBoxCategoryReturn_p_features({
    required this.type,
    required this.geometry,
    required this.properties,
  });

  factory SearchBoxCategoryReturn_p_features.fromJson(json) {
    return SearchBoxCategoryReturn_p_features(
      type: json['type'],
      geometry: SearchBoxGeometry.fromJson(json['geometry']),
      properties: SearchBoxProperties.fromJson(json['properties']),
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'type': type,
      'geometry': geometry.toJson(),
      'properties': properties.toJson(),
    };
    return jsonObject;
  }
}
