import { ApiProperty } from '@nestjs/swagger';
import { LocationItem } from 'src/models';

export class GetLatestLocationsReturn {
  @ApiProperty({
    type: [LocationItem],
    description: 'List of latest locations',
    example: [],
  })
  locations!: LocationItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of latest locations',
    example: 0,
    minimum: 0,
  })
  locationCount!: number;
}

export class GetLocationsReturn {
  @ApiProperty({
    type: [LocationItem],
    description: 'List of locations',
    example: [],
  })
  locations!: LocationItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of locations',
    example: 0,
    minimum: 0,
  })
  locationCount!: number;
}
