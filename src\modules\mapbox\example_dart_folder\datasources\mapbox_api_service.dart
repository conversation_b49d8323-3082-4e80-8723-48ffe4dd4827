import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:ivent_app/core/network/api_client.dart';

class MapboxApiService {
  authService.mapboxApi._();

  static Future<SearchBoxSuggestReturn?> searchBoxSuggest({
    required String q,
    required String sessionToken,
    String? language,
    int? limit,
    String? proximity,
    String? bbox,
    String? country,
    String? types,
    String? poiCategory,
    String? poiCategoryExclusions,
    String? etaType,
    String? navigationProfile,
    String? origin,
  }) async {
    final Map<String, dynamic> queryParameters = {
      'q': q,
      'access_token': dotenv.env['MAPBOX_ACCESS_TOKEN']!,
      'session_token': sessionToken,
    };

    if (language != null) queryParameters['language'] = language;
    if (limit != null) queryParameters['limit'] = limit;
    if (proximity != null) queryParameters['proximity'] = proximity;
    if (bbox != null) queryParameters['bbox'] = bbox;
    if (country != null) queryParameters['country'] = country;
    if (types != null) queryParameters['types'] = types;
    if (poiCategory != null) queryParameters['poi_category'] = poiCategory;
    if (poiCategoryExclusions != null) queryParameters['poi_category_exclusions'] = poiCategoryExclusions;
    if (etaType != null) queryParameters['eta_type'] = etaType;
    if (navigationProfile != null) queryParameters['navigation_profile'] = navigationProfile;
    if (origin != null) queryParameters['origin'] = origin;

    SearchBoxSuggestReturn? result;
    await BaseClient.safeApiCall(
      MapboxApiEndpoints.searchBoxSuggestUrl,
      RequestType.get,
      queryParameters: queryParameters,
      onSuccess: (response) {
        result = SearchBoxSuggestReturn.fromJson(response.data);
      },
    );

    return result;
  }

  static Future<SearchBoxRetrieveReturn?> searchBoxRetrieve({
    required String id,
    required String sessionToken,
    String? language,
    String? etaType,
    String? navigationProfile,
    String? origin,
  }) async {
    final Map<String, dynamic> queryParameters = {
      'access_token': dotenv.env['MAPBOX_ACCESS_TOKEN']!,
      'session_token': sessionToken,
    };

    if (language != null) queryParameters['language'] = language;
    if (etaType != null) queryParameters['eta_type'] = etaType;
    if (navigationProfile != null) queryParameters['navigation_profile'] = navigationProfile;
    if (origin != null) queryParameters['origin'] = origin;

    SearchBoxRetrieveReturn? result;
    await BaseClient.safeApiCall(
      MapboxApiEndpoints.searchBoxRetrieveUrl(id),
      RequestType.get,
      queryParameters: queryParameters,
      onSuccess: (response) {
        result = SearchBoxRetrieveReturn.fromJson(response.data);
      },
    );

    return result;
  }

  static Future<SearchBoxForwardReturn?> searchBoxForward({
    required String q,
    String? language,
    int? limit,
    String? proximity,
    String? bbox,
    String? country,
    String? types,
    String? poiCategory,
    String? poiCategoryExclusions,
    String? autoComplete,
    String? etaType,
    String? navigationProfile,
    String? origin,
  }) async {
    final Map<String, dynamic> queryParameters = {
      'q': q,
      'access_token': dotenv.env['MAPBOX_ACCESS_TOKEN']!,
    };

    if (language != null) queryParameters['language'] = language;
    if (limit != null) queryParameters['limit'] = limit;
    if (proximity != null) queryParameters['proximity'] = proximity;
    if (bbox != null) queryParameters['bbox'] = bbox;
    if (country != null) queryParameters['country'] = country;
    if (types != null) queryParameters['types'] = types;
    if (poiCategory != null) queryParameters['poi_category'] = poiCategory;
    if (poiCategoryExclusions != null) queryParameters['poi_category_exclusions'] = poiCategoryExclusions;
    if (autoComplete != null) queryParameters['auto_complete'] = autoComplete;
    if (etaType != null) queryParameters['eta_type'] = etaType;
    if (navigationProfile != null) queryParameters['navigation_profile'] = navigationProfile;
    if (origin != null) queryParameters['origin'] = origin;

    SearchBoxForwardReturn? result;
    await BaseClient.safeApiCall(
      MapboxApiEndpoints.searchBoxForwardUrl,
      RequestType.get,
      queryParameters: queryParameters,
      onSuccess: (response) {
        result = SearchBoxForwardReturn.fromJson(response.data);
      },
    );

    return result;
  }

  static Future<SearchBoxCategoryReturn?> searchBoxCategory({
    String? language,
    String? proximity,
    String? bbox,
    String? country,
    String? types,
    String? poiCategoryExclusions,
    String? sarType,
    String? route,
    String? routeGeometry,
    int? timeDeviation,
  }) async {
    final Map<String, dynamic> queryParameters = {
      'access_token': dotenv.env['MAPBOX_ACCESS_TOKEN']!,
    };

    if (language != null) queryParameters['language'] = language;
    if (proximity != null) queryParameters['proximity'] = proximity;
    if (bbox != null) queryParameters['bbox'] = bbox;
    if (country != null) queryParameters['country'] = country;
    if (types != null) queryParameters['types'] = types;
    if (poiCategoryExclusions != null) queryParameters['poi_category_exclusions'] = poiCategoryExclusions;
    if (sarType != null) queryParameters['sar_type'] = sarType;
    if (route != null) queryParameters['route'] = route;
    if (routeGeometry != null) queryParameters['route_geometry'] = routeGeometry;
    if (timeDeviation != null) queryParameters['time_deviation'] = timeDeviation;

    SearchBoxCategoryReturn? result;
    await BaseClient.safeApiCall(
      MapboxApiEndpoints.searchBoxCategoryListUrl,
      RequestType.get,
      queryParameters: queryParameters,
      onSuccess: (response) {
        result = SearchBoxCategoryReturn.fromJson(response.data);
      },
    );

    return result;
  }

  static Future<SearchBoxCategoryListReturn?> searchBoxCategoryList({
    String? language,
  }) async {
    final Map<String, dynamic> queryParameters = {
      'access_token': dotenv.env['MAPBOX_ACCESS_TOKEN']!,
    };

    if (language != null) queryParameters['language'] = language;

    SearchBoxCategoryListReturn? result;
    await BaseClient.safeApiCall(
      MapboxApiEndpoints.searchBoxCategoryListUrl,
      RequestType.get,
      queryParameters: queryParameters,
      onSuccess: (response) {
        result = SearchBoxCategoryListReturn.fromJson(response.data);
      },
    );

    return result;
  }

  static Future<SearchBoxReverseReturn?> searchBoxReverse({
    required double longitude,
    required double latitude,
    String? language,
    int? limit,
    String? country,
    String? types,
  }) async {
    final Map<String, dynamic> queryParameters = {
      'access_token': dotenv.env['MAPBOX_ACCESS_TOKEN']!,
      'longitude': longitude,
      'latitude': latitude,
    };

    if (language != null) queryParameters['language'] = language;
    if (limit != null) queryParameters['limit'] = limit;
    if (country != null) queryParameters['country'] = country;
    if (types != null) queryParameters['types'] = types;

    SearchBoxReverseReturn? result;
    await BaseClient.safeApiCall(
      MapboxApiEndpoints.searchBoxReverseUrl,
      RequestType.get,
      queryParameters: queryParameters,
      onSuccess: (response) {
        result = SearchBoxReverseReturn.fromJson(response.data);
      },
    );

    return result;
  }
}
