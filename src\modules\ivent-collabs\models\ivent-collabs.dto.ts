import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsUUID } from 'class-validator';
import { AccountTypeEnum } from 'src/entities';

export class RemoveCollabByIventIdDto {
  @ApiProperty({
    description: 'UUID of the collaborator to be removed from the ivent',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsUUID('4', { message: 'collabId must be a valid UUID v4' })
  collabId!: string;

  @ApiProperty({
    enum: AccountTypeEnum,
    enumName: 'AccountTypeEnum',
    description: 'Type of the collaborator account (user or page)',
    example: AccountTypeEnum.USER,
  })
  @IsEnum(AccountTypeEnum, { message: 'collabType must be a valid AccountTypeEnum value' })
  collabType!: AccountTypeEnum;
}
