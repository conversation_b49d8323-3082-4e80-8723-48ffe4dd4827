import { Controller, DefaultValuePipe, Get, ParseIntPipe, Query, Res } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { LocationsService } from './locations.service';
import { GetLatestLocationsReturn, GetLocationsReturn } from './models/locations.returns';

@ApiTags('locations')
@Controller('locations')
export class LocationsController {
  constructor(private readonly locationsService: LocationsService) {}

  @ApiOperation({
    summary: 'Haritadan gözüken bölgedeki konumları listeler (hem GoogleMapsAPI hem de kendi konumlarımız olabilir)',
  })
  @ApiResponse({
    status: 200,
    type: GetLocationsReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get('search')
  async searchLocations(
    @Res({ passthrough: true }) res: Response,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.locationsService.getLocations({
      sessionId,
      sessionRole,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Son seçilen konumları listeler',
  })
  @ApiResponse({
    status: 200,
    type: GetLatestLocationsReturn,
  })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get('latest')
  async getLatestLocations(
    @Res({ passthrough: true }) res: Response,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.locationsService.getLatestLocations({
      sessionId,
      sessionRole,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
}
