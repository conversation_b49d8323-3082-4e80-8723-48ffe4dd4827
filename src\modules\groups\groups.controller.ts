import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Post, Res } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { GroupsService } from './groups.service';
import { CreateGroupDto } from './models/groups.dto';
import { CreateGroupReturn, GetGroupByGroupIdReturn } from './models/groups.returns';

@ApiTags('groups')
@Controller('groups')
export class GroupsController {
  constructor(private readonly groupsService: GroupsService) {}

  @ApiOperation({
    summary: 'Arkadaş grubu oluşturur',
  })
  @ApiResponse({
    status: 200,
    type: CreateGroupReturn,
  })
  @Post('create')
  async createGroup(@Body() createGroupDto: CreateGroupDto, @Res({ passthrough: true }) res: Response) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.groupsService.createGroup({
      sessionId,
      sessionRole,
      ...createGroupDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
  @ApiOperation({
    summary: 'Grubun IDsi ile arkadaş grubunu siler',
  })
  @ApiResponse({ status: 200 })
  @Delete(':id/delete')
  async deleteGroupByGroupId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) groupId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.groupsService.deleteGroupByGroupId({
      sessionId,
      sessionRole,
      groupId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Grubun IDsi ile arkadaş grubundaki hesapları listeler',
  })
  @ApiResponse({
    status: 200,
    type: GetGroupByGroupIdReturn,
  })
  @Get(':id')
  async getGroupByGroupId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) groupId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.groupsService.getGroupByGroupId({
      sessionId,
      sessionRole,
      groupId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
}
