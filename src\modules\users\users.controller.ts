import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { IventListingTypeEnum } from 'src/constants/enums';
import {
  GetContactsByUserIdDto,
  RegisterDto,
  RemoveFollowerByUserIdDto,
  UpdateByUserIdDto,
  UpdateEmailByUserIdDto,
  UpdateGradByUserIdDto,
  UpdateNotificationsByUserIdDto,
  UpdatePhoneNumberByUserIdDto,
} from './models/users.dto';
import {
  GetContactsByUserIdReturn,
  GetFavoritesByUserIdReturn,
  GetFollowerFriendsByUserIdReturn,
  GetFollowersByUserIdReturn,
  GetFollowingsByUserIdReturn,
  GetIventsByUserIdReturn,
  GetLevelByUserIdReturn,
  GetMemoryFoldersByUserIdReturn,
  GetPagesByUserIdReturn,
  GetUserBannerByUserIdReturn,
  GetUserByUserIdReturn,
  GetVibeFoldersByUserIdReturn,
  RegisterReturn,
} from './models/users.returns';
import { UsersService } from './users.service';

@ApiTags('users')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @ApiOperation({
    summary: 'Hesaba giriş yapılır',
  })
  @ApiResponse({
    status: 200,
    type: RegisterReturn,
  })
  @Post('register')
  async register(@Body() registerDto: RegisterDto, @Res({ passthrough: true }) res: Response) {
    const result = await this.usersService.register(registerDto);
    const sessionId = result.token;
    res.cookie('auth-token', sessionId, { httpOnly: true });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile hesap silinir',
  })
  @ApiResponse({ status: 200 })
  @Delete(':id/delete')
  async deleteByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.deleteByUserId({
      sessionId,
      sessionRole,
      userId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile hesap bilgileri listelenir',
  })
  @ApiResponse({
    status: 200,
    type: GetUserByUserIdReturn,
  })
  @Get(':id')
  async getByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.getByUserId({
      sessionId,
      sessionRole,
      userId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile rehber bağlantıları listelenir',
  })
  @ApiResponse({
    status: 200,
    type: GetContactsByUserIdReturn,
  })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Post(':id/contacts')
  async getContactsByUserId(
    @Body() getContactsByUserId: GetContactsByUserIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.getContactsByUserId({
      sessionId,
      sessionRole,
      userId,
      limit,
      page,
      ...getContactsByUserId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile hesabın favoriledikleri listelenir',
  })
  @ApiResponse({
    status: 200,
    type: GetFavoritesByUserIdReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':id/favorites')
  async getFavoritesByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.getFavoritesByUserId({
      sessionId,
      sessionRole,
      userId,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile hesabın takip ettikleri listelenir',
  })
  @ApiResponse({
    status: 200,
    type: GetFollowingsByUserIdReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':id/followings')
  async getFollowingsByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.getFollowingsByUserId({
      sessionId,
      sessionRole,
      userId,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile hesabın katıldığı ya da oluşturduğu iventler listelenir',
  })
  @ApiResponse({
    status: 200,
    type: GetIventsByUserIdReturn,
  })
  @ApiQuery({
    name: 'type',

    enum: IventListingTypeEnum,
    enumName: 'IventListingTypeEnum',
    description: 'Type of ivents to list - either joined or created by the user',
    example: IventListingTypeEnum.JOINED,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':id/ivents')
  async getIventsByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
    @Query('type') type: IventListingTypeEnum,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.getIventsByUserId({
      sessionId,
      sessionRole,
      userId,
      type,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile hesabın aşaması gösterilir',
  })
  @ApiResponse({
    status: 200,
    type: GetLevelByUserIdReturn,
  })
  @Get(':id/level')
  async getLevelByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.getLevelByUserId({
      sessionId,
      sessionRole,
      userId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
  @ApiOperation({
    summary: 'Hesap IDsi ile hesaba ait sayfalar gösterilir',
  })
  @ApiResponse({
    status: 200,
    type: GetPagesByUserIdReturn,
  })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':id/pages')
  async getPagesByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.getPagesByUserId({
      sessionId,
      sessionRole,
      userId,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile hesabın memoriesleri listelenir',
  })
  @ApiResponse({
    status: 200,
    type: GetMemoryFoldersByUserIdReturn,
  })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':id/memories')
  async getMemoryFoldersByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.getMemoryFoldersByUserId({
      sessionId,
      sessionRole,
      userId,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile hesabın vibeları listelenir',
  })
  @ApiResponse({
    status: 200,
    type: GetVibeFoldersByUserIdReturn,
  })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':id/vibes')
  async getVibeFoldersByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.getVibeFoldersByUserId({
      sessionId,
      sessionRole,
      userId,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile hesabın detayları güncellenir',
  })
  @ApiResponse({ status: 200 })
  @Put(':id/update')
  async updateByUserId(
    @Body() updateByUserIdDto: UpdateByUserIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.updateByUserId({
      sessionId,
      sessionRole,
      userId,
      ...updateByUserIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile emaili güncellenir',
  })
  @ApiResponse({ status: 200 })
  @Put(':id/update/email')
  async updateEmailByUserId(
    @Body() updateEmailByUserIdDto: UpdateEmailByUserIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.updateEmailByUserId({
      sessionId,
      sessionRole,
      userId,
      ...updateEmailByUserIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile mezuniyet durumu güncellenir',
  })
  @ApiResponse({ status: 200 })
  @Put(':id/update/grad')
  async updateGradByUserId(
    @Body() updateGradByUserIdDto: UpdateGradByUserIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.updateGradByUserId({
      sessionId,
      sessionRole,
      userId,
      ...updateGradByUserIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile bildirim ayarları güncellenir',
  })
  @ApiResponse({ status: 200 })
  @Put(':id/update/notifications')
  async updateNotificationsByUserId(
    @Body() updateNotificationsByUserIdDto: UpdateNotificationsByUserIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.updateNotificationsByUserId({
      sessionId,
      sessionRole,
      userId,
      ...updateNotificationsByUserIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile telefon numarası güncellenir',
  })
  @ApiResponse({ status: 200 })
  @Put(':id/update/phone-number')
  async updatePhoneNumberByUserId(
    @Body() updatePhoneNumberByUserIdDto: UpdatePhoneNumberByUserIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.updatePhoneNumberByUserId({
      sessionId,
      sessionRole,
      userId,
      ...updatePhoneNumberByUserIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile gizlilik ayarları güncellenir',
  })
  @ApiResponse({ status: 200 })
  @Put(':id/update/privacy')
  async updatePrivacyByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.updatePrivacyByUserId({
      sessionId,
      sessionRole,
      userId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile takipçileri listeler',
  })
  @ApiResponse({
    status: 200,
    type: GetFollowersByUserIdReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':id/followers')
  async getFollowersByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
    @Param('q') q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.getFollowersByUserId({
      sessionId,
      sessionRole,
      userId,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile takipçilerden arkadaş olunanları listeler',
  })
  @ApiResponse({
    status: 200,
    type: GetFollowerFriendsByUserIdReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':id/followers/friends')
  async getFollowerFriendsByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
    @Param('q') q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.getFollowerFriendsByUserId({
      sessionId,
      sessionRole,
      userId,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile hesap takip edilir',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/follow')
  async followByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.followByUserId({
      sessionId,
      sessionRole,
      userId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile hesabın takibinden çıkılır',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/unfollow')
  async unfollowByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.unfollowByUserId({
      sessionId,
      sessionRole,
      userId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile hesabın takipçilerinden kaldırılır',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/followers/remove')
  async removeFollowerByUserId(
    @Body() removeFollowerByUserIdDto: RemoveFollowerByUserIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.removeFollowerByUserId({
      sessionId,
      sessionRole,
      userId,
      ...removeFollowerByUserIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile hesap bildirimleri açılır',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/subscribe')
  async subscribeByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.subscribeByUserId({
      sessionId,
      sessionRole,
      userId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile hesap bildirimleri kapatılır',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/unsubscribe')
  async unsubscribeByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.unsubscribeByUserId({
      sessionId,
      sessionRole,
      userId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Email doğrulaması için gönderilen kod doğrulanır',
  })
  @ApiResponse({ status: 200 })
  @Post('emailVerification')
  async validateEmail(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.validateEmail({
      sessionId,
      sessionRole,
      userId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Email doğrulaması için kod gönderilir',
  })
  @ApiResponse({ status: 200 })
  @Post('send-verification-email')
  async sendVerificationEmail(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.sendVerificationEmail({
      sessionId,
      sessionRole,
      userId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Creator başvuru formu gönderilir',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/send-creator-request')
  async sendCreatorRequestForm(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.sendCreatorRequestForm({
      sessionId,
      sessionRole,
      userId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: "Hesap IDsi ile hesabın avatar URL'si ve ismi listelenir",
  })
  @ApiResponse({
    status: 200,
    type: GetUserBannerByUserIdReturn,
  })
  @Get(':id/banner')
  async getUserBannerByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.usersService.getUserBannerByUserId({
      sessionId,
      sessionRole,
      userId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
}
