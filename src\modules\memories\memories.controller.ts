import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Post, Query, Res } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { MemoryOriginEnum } from 'src/constants/enums/memory-origin-enum';
import { MemoriesService } from './memories.service';
import { CreateMemoryDto } from './models/memories.dto';
import { CreateMemoryReturn, GetMemoryByMemoryIdReturn } from './models/memories.returns';

@ApiTags('memories')
@Controller('memories')
export class MemoriesController {
  constructor(private readonly memoriesService: MemoriesService) {}

  @ApiOperation({
    summary: 'Memory oluşturur',
  })
  @ApiResponse({
    status: 200,
    type: CreateMemoryReturn,
  })
  @Post('create')
  async createMemory(@Body() createMemoryDto: CreateMemoryDto, @Res({ passthrough: true }) res: Response) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.memoriesService.createMemory({
      sessionId,
      sessionRole,
      ...createMemoryDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Memory IDsi ile memory siler',
  })
  @ApiResponse({ status: 200 })
  @Delete(':id/delete')
  async deleteMemoryByMemoryId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) memoryId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.memoriesService.deleteMemoryByMemoryId({
      sessionId,
      sessionRole,
      memoryId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'ID ile memory getirir',
  })
  @ApiResponse({
    status: 200,
    type: GetMemoryByMemoryIdReturn,
  })
  @ApiQuery({
    name: 'origin',

    enum: MemoryOriginEnum,
    enumName: 'MemoryOriginEnum',
    description: 'Origin of the memory',
    example: MemoryOriginEnum.CREATE_NEW,
  })
  @Get(':id')
  async getMemoryByMemoryId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) memoryId: string,
    @Query('origin') origin: MemoryOriginEnum,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.memoriesService.getMemoryByMemoryId({
      sessionId,
      sessionRole,
      memoryId,
      origin,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
}
