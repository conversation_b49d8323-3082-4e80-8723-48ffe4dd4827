import { ApiProperty } from '@nestjs/swagger';
import { UniversityItem } from 'src/models';

export class SearchUniversitiesReturn {
  @ApiProperty({
    type: [UniversityItem],
    description: 'List of universities',
    example: [],
  })
  universities!: UniversityItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of universities',
    example: 0,
    minimum: 0,
  })
  universityCount!: number;
}
