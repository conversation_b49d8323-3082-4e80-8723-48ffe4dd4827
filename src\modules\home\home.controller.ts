import { Controller, DefaultValuePipe, Get, ParseIntPipe, Query, Res } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { FeedDateEnum } from 'src/constants/enums/feed-date-enum';
import { HomeService } from './home.service';
import { FeedReturn, MapReturn, SearchAccountReturn, SearchIventReturn } from './models/home.returns';

@ApiTags('home')
@Controller('')
export class HomeController {
  constructor(private readonly homeService: HomeService) {}

  @ApiOperation({
    summary: 'Feedi listeler',
    description: 'Şu anda feedi listelerken sadece dummy verileri döndürüyoruz. İleride parametre ile çalışacak.',
  })
  @ApiResponse({
    status: 200,
    type: FeedReturn,
  })
  @ApiQuery({
    name: 'dateType',
    enum: FeedDateEnum,
    enumName: 'FeedDateEnum',
    description: 'Date range to filter the feed',
    example: FeedDateEnum.TODAY,
  })
  @ApiQuery({
    name: 'startDate',
    example: '2021-12-31',
    description: 'Start date for the feed',
    required: false,
  })
  @ApiQuery({
    name: 'endDate',
    example: '2021-12-31',
    description: 'End date for the feed',
    required: false,
  })
  @ApiQuery({ name: 'locationCoeff', type: 'integer' })
  @ApiQuery({ name: 'latStart', type: 'double', example: 40.7766 })
  @ApiQuery({ name: 'latEnd', type: 'double', example: 40.7766 })
  @ApiQuery({ name: 'lngStart', type: 'double', example: -73.9712 })
  @ApiQuery({ name: 'lngEnd', type: 'double', example: -73.9712 })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get('feed')
  async feed(
    @Res({ passthrough: true }) res: Response,
    @Query('dateType') dateType: FeedDateEnum,
    @Query('startDate', new DefaultValuePipe('')) startDate: string,
    @Query('endDate', new DefaultValuePipe('')) endDate: string,
    @Query('categories') categories: string,
    @Query('locationCoeff', ParseIntPipe) locationCoeff: number,
    @Query('latStart', ParseIntPipe) latStart: number,
    @Query('latEnd', ParseIntPipe) latEnd: number,
    @Query('lngStart', ParseIntPipe) lngStart: number,
    @Query('lngEnd', ParseIntPipe) lngEnd: number,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.homeService.feed({
      sessionId,
      sessionRole,
      dateType,
      startDate,
      endDate,
      categories: categories.split(',').filter((x) => x.trim() !== ''),
      locationCoeff,
      latStart,
      latEnd,
      lngStart,
      lngEnd,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Mapi listeler',
    description: 'Şu anda mapi listelerken sadece dummy verileri döndürüyoruz. İleride parametre ile çalışacak.',
  })
  @ApiResponse({
    status: 200,
    type: MapReturn,
  })
  @ApiQuery({
    name: 'startDate',
    example: '2021-12-31',
    description: 'Start date for the ivents on map',
  })
  @ApiQuery({
    name: 'endDate',
    example: '2021-12-31',
    description: 'End date for the ivents on map',
  })
  @ApiQuery({ name: 'latStart', type: 'double', example: 40.7766 })
  @ApiQuery({ name: 'latEnd', type: 'double', example: 40.7766 })
  @ApiQuery({ name: 'lngStart', type: 'double', example: -73.9712 })
  @ApiQuery({ name: 'lngEnd', type: 'double', example: -73.9712 })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @Get('map')
  async map(
    @Res({ passthrough: true }) res: Response,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('latStart', ParseIntPipe) latStart: number,
    @Query('latEnd', ParseIntPipe) latEnd: number,
    @Query('lngStart', ParseIntPipe) lngStart: number,
    @Query('lngEnd', ParseIntPipe) lngEnd: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.homeService.map({
      sessionId,
      sessionRole,
      startDate,
      endDate,
      latStart,
      latEnd,
      lngStart,
      lngEnd,
      limit,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Uygulamada arama yapar',
  })
  @ApiResponse({
    status: 200,
    type: SearchIventReturn,
  })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get('searchIvent')
  async searchIvent(
    @Res({ passthrough: true }) res: Response,
    @Query('q') q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.homeService.searchIvent({
      sessionId,
      sessionRole,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Uygulamada arama yapar',
  })
  @ApiResponse({
    status: 200,
    type: SearchAccountReturn,
  })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get('searchAccount')
  async searchAccount(
    @Res({ passthrough: true }) res: Response,
    @Query('q') q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.homeService.searchAccount({
      sessionId,
      sessionRole,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
}
