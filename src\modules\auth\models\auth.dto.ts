import { ApiProperty } from '@nestjs/swagger';
import { IsString, Matches } from 'class-validator';

export class SendVerificationCodeDto {
  @ApiProperty({
    example: '+90(500)4003020',
    description: 'Phone number in international format with country code',
  })
  @IsString()
  @Matches(/^\+\d{1,3}\(\d{3}\)\d{7}$/, {
    message: 'Phone number must be in format +XX(XXX)XXXXXXX',
  })
  phoneNumber!: string;
}

export class ValidateDto {
  @ApiProperty({
    example: '123456',
    description: 'Six-digit verification code sent to the phone number',
    minLength: 6,
    maxLength: 6,
  })
  @IsString()
  @Matches(/^\d{6}$/, {
    message: 'Validation code must be exactly 6 digits',
  })
  validationCode!: string;

  @ApiProperty({
    example: '+90(500)4003020',
    description: 'Phone number in international format with country code',
  })
  @IsString()
  @Matches(/^\+\d{1,3}\(\d{3}\)\d{7}$/, {
    message: 'Phone number must be in format +XX(XXX)XXXXXXX',
  })
  phoneNumber!: string;
}
