import { Body, Controller, Delete, Param, ParseU<PERSON><PERSON><PERSON><PERSON>, Post, Res } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { CommentsService } from './comments.service';
import { CreateCommentDto } from './models/comments.dto';
import { CreateCommentReturn } from './models/comments.returns';

@ApiTags('comments')
@Controller('comments')
export class CommentsController {
  constructor(private readonly commentsService: CommentsService) {}
  @ApiOperation({
    summary: 'Yorum oluşturur',
  })
  @ApiResponse({
    status: 200,
    type: CreateCommentReturn,
  })
  @Post('create')
  async createComment(@Body() createCommentDto: CreateCommentDto, @Res({ passthrough: true }) res: Response) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.commentsService.createComment({
      sessionId,
      sessionRole,
      ...createCommentDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Yorumun IDsi ile yorumu siler',
  })
  @ApiResponse({ status: 200 })
  @Delete(':id/delete')
  async deleteCommentByCommentId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) commentId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.commentsService.deleteCommentByCommentId({
      sessionId,
      sessionRole,
      commentId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
}
