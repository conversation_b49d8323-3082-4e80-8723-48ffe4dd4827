import { VibePrivacyEnum } from 'src/entities';
import { MediaFormatEnum } from 'src/entities/enums/shared/media-format-enum';

export type CreateVibeParams = {
  sessionId: string;
  sessionRole: string;
  fileBuffer?: string | null;
  mediaUrl?: string | null;
  mediaFormat: MediaFormatEnum;
  caption?: string | null;
  squadId: string;
  privacy: VibePrivacyEnum;
};

export type DeleteVibeByVibeIdParams = {
  sessionId: string;
  sessionRole: string;
  vibeId: string;
};

export type GetCommentsByVibeIdParams = {
  sessionId: string;
  sessionRole: string;
  vibeId: string;
  limit: number;
  page: number;
};

export type GetLikesByVibeIdParams = {
  sessionId: string;
  sessionRole: string;
  vibeId: string;
};

export type GetVibeByVibeIdParams = {
  sessionId: string;
  sessionRole: string;
  vibeId: string;
};

export type GetVibesParams = {
  sessionId: string;
  sessionRole: string;
  limit: number;
  page: number;
};

export type HideByVibeIdParams = {
  sessionId: string;
  sessionRole: string;
  vibeId: string;
};

export type LikeByVibeIdParams = {
  sessionId: string;
  sessionRole: string;
  vibeId: string;
};

export type ShowByVibeIdParams = {
  sessionId: string;
  sessionRole: string;
  vibeId: string;
};

export type UnlikeByVibeIdParams = {
  sessionId: string;
  sessionRole: string;
  vibeId: string;
};

export type UpdateByVibeIdParams = {
  sessionId: string;
  sessionRole: string;
  vibeId: string;
  newCaption: string;
};
