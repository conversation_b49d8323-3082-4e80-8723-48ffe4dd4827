import { ApiProperty } from '@nestjs/swagger';
import { PageMembershipStatusEnum } from 'src/entities';

export class SideMenuPageItem {
  @ApiProperty({
    description: 'UUID of the page',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  pageId!: string;

  @ApiProperty({
    description: 'Name of the page',
    example: 'Photography Club Istanbul',
  })
  pageName!: string;

  @ApiProperty({
    enum: PageMembershipStatusEnum,
    enumName: 'PageMembershipStatusEnum',
    description: 'Status of the user in the page',
    example: PageMembershipStatusEnum.ADMIN,
  })
  pageMembershipStatus!: PageMembershipStatusEnum;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'URL to the page thumbnail image',
    example: 'https://example.com/page-thumbnail.jpg',
    format: 'url',
  })
  thumbnailUrl!: string | null;
}
