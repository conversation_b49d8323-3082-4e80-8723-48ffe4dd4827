import { ApiProperty } from '@nestjs/swagger';
import { MarkerItem } from 'src/models';
import { BasicAccountListItem } from 'src/models/basic-account-list-item';
import { IventCardItem } from 'src/models/ivent-card-item';

export class FeedReturn {
  @ApiProperty({
    type: [IventCardItem],
    description: 'List of ivents',
    example: [],
  })
  ivents!: IventCardItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of ivents',
    example: 0,
    minimum: 0,
  })
  iventCount!: number;
}

export class MapReturn {
  @ApiProperty({
    type: [MarkerItem],
    description: 'List of ivent markers',
    example: [],
  })
  ivents!: MarkerItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of ivent markers',
    example: 0,
    minimum: 0,
  })
  iventCount!: number;
}

export class SearchAccountReturn {
  @ApiProperty({
    type: [BasicAccountListItem],
    description: 'List of accounts',
    example: [],
  })
  accounts!: BasicAccountListItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of accounts',
    example: 0,
    minimum: 0,
  })
  accountCount!: number;
}

export class SearchIventReturn {
  @ApiProperty({
    type: [IventCardItem],
    description: 'List of ivents',
    example: [],
  })
  ivents!: IventCardItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of ivents',
    example: 0,
    minimum: 0,
  })
  iventCount!: number;
}
