import { ApiProperty } from '@nestjs/swagger';

export class MemoryFolderCardItem {
  @ApiProperty({
    description: 'UUID of the memory folder',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  memoryFolderId!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'URL to the memory folder thumbnail image',
    example: 'https://example.com/thumbnail.jpg',
    format: 'url',
  })
  thumbnailUrl!: string | null;

  @ApiProperty({
    description: 'UUID of the ivent',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  iventId!: string;

  @ApiProperty({
    description: 'Name of the ivent',
    example: 'My Awesome Ivent',
  })
  iventName!: string;

  @ApiProperty({
    format: 'date-time',
    description: 'List of dates for the ivent',
    example: ['2024-08-31T22:00:00Z', '2024-09-01T14:00:00Z'],
  })
  dates!: string[];

  @ApiProperty({
    type: 'integer',
    description: 'Number of members in the ivent',
    example: 10,
    minimum: 0,
  })
  memberCount!: number;

  @ApiProperty({
    description: "List of member's first names in the ivent",
    example: ['John', 'Jane'],
  })
  memberFirstnames!: string[];

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'Date of creation of the memory folder',
    example: '2024-08-31T22:00:00Z',
    format: 'date-time',
  })
  createdAt!: string | null;
}
