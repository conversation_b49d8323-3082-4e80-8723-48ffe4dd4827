import { ApiProperty } from '@nestjs/swagger';
import { UserListItem, UserListItemWithGroupRole } from 'src/models';

export class SearchGroupMembersByGroupIdReturn {
  @ApiProperty({
    type: [UserListItemWithGroupRole],
    description: 'List of group members with their roles and friendship status',
    example: [],
  })
  members!: UserListItemWithGroupRole[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of group members',
    example: 25,
    minimum: 0,
  })
  memberCount!: number;
}

export class SearchInvitableUsersByGroupIdReturn {
  @ApiProperty({
    type: [UserListItem],
    description: 'List of users that can be invited to the group',
    example: [],
  })
  users!: UserListItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of invitable users',
    example: 10,
    minimum: 0,
  })
  userCount!: number;
}

export class SearchUsersForGroupCreationReturn {
  @ApiProperty({
    type: [UserListItem],
    description: 'List of users available for group creation',
    example: [],
  })
  users!: UserListItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of users available for group creation',
    example: 15,
    minimum: 0,
  })
  userCount!: number;
}
