import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { CreateVibeDto, UpdateByVibeIdDto } from './models/vibes.dto';
import {
  CreateVibeReturn,
  GetCommentsByVibeIdReturn,
  GetLikesByVibeIdReturn,
  GetVibeByVibeIdReturn,
  GetVibesReturn,
} from './models/vibes.returns';
import { VibesService } from './vibes.service';

@ApiTags('vibes')
@Controller('vibes')
export class VibesController {
  constructor(private readonly vibesService: VibesService) {}

  @ApiOperation({
    summary: 'Vibe oluşturulur',
  })
  @ApiResponse({
    status: 200,
    type: CreateVibeReturn,
  })
  @Post('create')
  async createVibe(@Body() createVibeDto: CreateVibeDto, @Res({ passthrough: true }) res: Response) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.vibesService.createVibe({
      sessionId,
      sessionRole,
      ...createVibeDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe silinir',
  })
  @ApiResponse({ status: 200 })
  @Delete(':id/delete')
  async deleteByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.vibesService.deleteByVibeId({
      sessionId,
      sessionRole,
      vibeId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Vibe sekmesi listelenir',
  })
  @ApiResponse({
    status: 200,
    type: GetVibesReturn,
  })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get('')
  async getVibes(
    @Res({ passthrough: true }) res: Response,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.vibesService.getVibes({
      sessionId,
      sessionRole,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe getirilir',
  })
  @ApiResponse({
    status: 200,
    type: GetVibeByVibeIdReturn,
  })
  @Get(':id')
  async getByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.vibesService.getByVibeId({
      sessionId,
      sessionRole,
      vibeId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe yorumları listelenir',
  })
  @ApiResponse({
    status: 200,
    type: GetCommentsByVibeIdReturn,
  })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':id/comments')
  async getCommentsByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.vibesService.getCommentsByVibeId({
      sessionId,
      sessionRole,
      vibeId,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe bilgileri güncellenir',
  })
  @ApiResponse({ status: 200 })
  @Put(':id/update')
  async updateByVibeId(
    @Body() updateByVibeIdDto: UpdateByVibeIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.vibesService.updateByVibeId({
      sessionId,
      sessionRole,
      vibeId,
      ...updateByVibeIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe beğenileri listelenir',
  })
  @ApiResponse({
    status: 200,
    type: GetLikesByVibeIdReturn,
  })
  @Get(':id/likes')
  async getLikesByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.vibesService.getLikesByVibeId({
      sessionId,
      sessionRole,
      vibeId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe beğenilir',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/likes/like')
  async likeByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.vibesService.likeByVibeId({
      sessionId,
      sessionRole,
      vibeId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe beğenisi kaldırılır',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/likes/unlike')
  async unlikeByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.vibesService.unlikeByVibeId({
      sessionId,
      sessionRole,
      vibeId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe gizlenir',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/hide')
  async hideByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.vibesService.hideByVibeId({
      sessionId,
      sessionRole,
      vibeId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe gösterime girer',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/show')
  async showByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.vibesService.showByVibeId({
      sessionId,
      sessionRole,
      vibeId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
}
