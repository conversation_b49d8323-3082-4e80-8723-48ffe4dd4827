import { ApiProperty } from '@nestjs/swagger';
import { NotificationItem } from 'src/models';

export class GetNotificationsReturn {
  @ApiProperty({
    type: [NotificationItem],
    description: 'List of user notifications',
    example: [],
  })
  notifications!: NotificationItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of notifications',
    example: 15,
    minimum: 0,
  })
  notificationCount!: number;
}
