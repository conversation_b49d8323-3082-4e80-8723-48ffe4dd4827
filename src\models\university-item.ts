import { ApiProperty } from '@nestjs/swagger';

export class UniversityItem {
  @ApiProperty({
    description: 'Name of the university',
    example: 'Boğaziçi University',
  })
  universityName!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'URL to the university image',
    example: 'https://example.com/university.jpg',
    format: 'url',
  })
  universityImageUrl!: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'State where the university is located',
    example: 'Istanbul',
  })
  universityLocationState!: string | null;
}
