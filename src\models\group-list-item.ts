import { ApiProperty } from '@nestjs/swagger';

export class GroupListItem {
  @ApiProperty({
    description: 'UUID of the group',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  groupId!: string;

  @ApiProperty({
    description: 'Name of the group',
    example: 'Photography Enthusiasts',
  })
  groupName!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'URL to the group thumbnail image',
    example: 'https://example.com/group-thumbnail.jpg',
    format: 'url',
  })
  thumbnailUrl!: string | null;

  @ApiProperty({
    description: "List of member's first names in the group",
    example: ['<PERSON>', '<PERSON>'],
  })
  memberFirstnames!: string[];

  @ApiProperty({
    type: 'integer',
    description: 'Number of members in the group',
    example: 5,
    minimum: 0,
  })
  memberCount!: number;
}
