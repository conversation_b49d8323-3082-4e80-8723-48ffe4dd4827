import { FeedDateEnum } from 'src/constants/enums';

export type FeedParams = {
  sessionId: string;
  sessionRole: string;
  dateType: FeedDateEnum;
  startDate: string;
  endDate: string;
  categories: string[];
  locationCoeff: number;
  latStart: number;
  latEnd: number;
  lngStart: number;
  lngEnd: number;
  q: string;
  limit: number;
  page: number;
};

export type MapParams = {
  sessionId: string;
  sessionRole: string;
  startDate: string;
  endDate: string;
  latStart: number;
  latEnd: number;
  lngStart: number;
  lngEnd: number;
  limit: number;
};

export type SearchAccountParams = {
  sessionId: string;
  sessionRole: string;
  q: string;
  limit: number;
  page: number;
};

export type SearchIventParams = {
  sessionId: string;
  sessionRole: string;
  q: string;
  limit: number;
  page: number;
};
