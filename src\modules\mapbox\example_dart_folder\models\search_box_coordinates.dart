class SearchBoxCoordinates {
  final double longitude;
  final double latitude;
  final String? accuracy;
  final List<SearchBoxRoutablePoints>? routable_points;

  SearchBoxCoordinates({
    required this.longitude,
    required this.latitude,
    required this.accuracy,
    required this.routable_points,
  });

  factory SearchBoxCoordinates.fromJson(json) {
    return SearchBoxCoordinates(
      longitude: json['longitude'],
      latitude: json['latitude'],
      accuracy: json['accuracy'],
      routable_points: json['routable_points'] != null
          ? (json['routable_points'] as List)
              .map<SearchBoxRoutablePoints>((val) => SearchBoxRoutablePoints.fromJson(val))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'longitude': longitude,
      'latitude': latitude,
      'accuracy': accuracy,
      'routable_points': routable_points?.map((SearchBoxRoutablePoints val) => val.toJson()).toList(),
    };
    return jsonObject;
  }
}
