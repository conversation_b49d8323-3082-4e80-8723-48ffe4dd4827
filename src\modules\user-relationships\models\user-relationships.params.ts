import { FriendListingTypeEnum } from 'src/constants/enums';

export type BlockUserByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
};

export type GetUserBlocklistParams = {
  sessionId: string;
  sessionRole: string;
};

export type InviteFriendByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
};

export type RemoveFriendByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
};

export type SearchFriendsByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
  type: FriendListingTypeEnum;
  q: string;
  limit: number;
  page: number;
};

export type UnblockUserByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
};

export type UninviteFriendByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
};
