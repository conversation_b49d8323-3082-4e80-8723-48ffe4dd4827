import {
  <PERSON>,
  DefaultV<PERSON>uePipe,
  Get,
  Param,
  ParseIntPipe,
  ParseUUIDPipe,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { FriendListingTypeEnum } from 'src/constants/enums';
import { GetUserBlocklistReturn, SearchFriendsByUserIdReturn } from './models/user-relationships.returns';
import { UserRelationshipsService } from './user-relationships.service';

@ApiTags('userRelationships')
@Controller('userRelationships')
export class UserRelationshipsController {
  constructor(private readonly userRelationshipsService: UserRelationshipsService) {}

  @ApiOperation({
    summary: 'Engellenenleri listeler',
  })
  @ApiResponse({
    status: 200,
    type: GetUserBlocklistReturn,
  })
  @Get('blocklist')
  async getUserBlocklist(@Res({ passthrough: true }) res: Response) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.userRelationshipsService.getUserBlocklist({
      sessionId,
      sessionRole,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile hesap engellenir',
  })
  @ApiResponse({ status: 200 })
  @Post(':userId/block')
  async blockUserByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('userId', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.userRelationshipsService.blockUserByUserId({
      sessionId,
      sessionRole,
      userId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile hesap engeli kaldırılır',
  })
  @ApiResponse({ status: 200 })
  @Post(':userId/unblock')
  async unblockUserByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('userId', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.userRelationshipsService.unblockUserByUserId({
      sessionId,
      sessionRole,
      userId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile arkadaşlar listelenir',
  })
  @ApiResponse({
    status: 200,
    type: SearchFriendsByUserIdReturn,
  })
  @ApiQuery({
    name: 'type',

    enum: FriendListingTypeEnum,
    enumName: 'FriendListingTypeEnum',
    description: 'Type of friends to list - either groups or users',
    example: FriendListingTypeEnum.USER,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':userId/friends')
  async searchFriendsByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('userId', new ParseUUIDPipe({ version: '4' })) userId: string,
    @Query('type') type: FriendListingTypeEnum,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.userRelationshipsService.searchFriendsByUserId({
      sessionId,
      sessionRole,
      userId,
      type,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile arkadaş daveti gönderilir',
  })
  @ApiResponse({ status: 200 })
  @Post(':userId/invite')
  async inviteFriendByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('userId', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.userRelationshipsService.inviteFriendByUserId({
      sessionId,
      sessionRole,
      userId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile arkadaş daveti gönderilir',
  })
  @ApiResponse({ status: 200 })
  @Post(':userId/uninvite')
  async uninviteFriendByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('userId', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.userRelationshipsService.uninviteFriendByUserId({
      sessionId,
      sessionRole,
      userId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Hesap IDsi ile arkadaşlıktan çıkılır',
  })
  @ApiResponse({ status: 200 })
  @Post(':userId/remove')
  async removeFriendByUserId(
    @Res({ passthrough: true }) res: Response,
    @Param('userId', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.userRelationshipsService.removeFriendByUserId({
      sessionId,
      sessionRole,
      userId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
}
