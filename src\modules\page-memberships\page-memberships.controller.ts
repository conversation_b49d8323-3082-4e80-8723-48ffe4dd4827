import {
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  Param,
  ParseIntPipe,
  ParseUUIDPipe,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import {
  AddPageMembersByPageIdDto,
  RemovePageMemberByPageIdDto,
  RemovePageModeratorByPageIdDto,
  TransferPageAdministrationByPageIdDto,
} from './models/page-memberships.dto';
import {
  SearchAdministrationByPageIdReturn,
  SearchModeratorsForPageCreationReturn,
  SearchPageMembersByPageIdReturn,
  SearchUsersToAddByPageIdReturn,
} from './models/page-memberships.returns';
import { PageMembershipsService } from './page-memberships.service';

@ApiTags('pageMemberships')
@Controller('pageMemberships')
export class PageMembershipsController {
  constructor(private readonly pageMembershipsService: PageMembershipsService) {}

  @ApiOperation({
    summary: 'Sayfa oluştururken uygun yardımcı adminler listelenir',
  })
  @ApiResponse({
    status: 200,
    type: SearchModeratorsForPageCreationReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get('administration/search')
  async searchModeratorsForPageCreation(
    @Res({ passthrough: true }) res: Response,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;
    const sessionPageId = res.locals.decoded && res.locals.decoded.p_id ? res.locals.decoded.p_id : null;

    const result = await this.pageMembershipsService.searchModeratorsForPageCreation({
      sessionId,
      sessionRole,
      sessionPageId,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Var olan sayfaya eklenebilecek uygun yardımcı adminler listelenir',
  })
  @ApiResponse({
    status: 200,
    type: SearchModeratorsForPageCreationReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':pageId/administration/search')
  async searchModeratorsToAddByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;
    const sessionPageId = res.locals.decoded && res.locals.decoded.p_id ? res.locals.decoded.p_id : null;

    const result = await this.pageMembershipsService.searchModeratorsToAddByPageId({
      sessionId,
      sessionRole,
      sessionPageId,
      pageId,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile adminlikten ayrılınır',
  })
  @ApiResponse({ status: 200 })
  @Post(':pageId/administration/leave')
  async leavePageModerationByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;
    const sessionPageId = res.locals.decoded && res.locals.decoded.p_id ? res.locals.decoded.p_id : null;

    const result = await this.pageMembershipsService.leavePageModerationByPageId({
      sessionId,
      sessionRole,
      sessionPageId,
      pageId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile yardımcı admin çıkartılır',
  })
  @ApiResponse({ status: 200 })
  @Post(':pageId/administration/remove')
  async removePageModeratorByPageId(
    @Body()
    removePageModeratorByPageIdDto: RemovePageModeratorByPageIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;
    const sessionPageId = res.locals.decoded && res.locals.decoded.p_id ? res.locals.decoded.p_id : null;

    const result = await this.pageMembershipsService.removePageModeratorByPageId({
      sessionId,
      sessionRole,
      sessionPageId,
      pageId,
      ...removePageModeratorByPageIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile adminlik devredilir',
  })
  @ApiResponse({ status: 200 })
  @Post(':pageId/administration/transfer')
  async transferPageAdministrationByPageId(
    @Body()
    transferPageAdministrationByPageIdDto: TransferPageAdministrationByPageIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;
    const sessionPageId = res.locals.decoded && res.locals.decoded.p_id ? res.locals.decoded.p_id : null;

    const result = await this.pageMembershipsService.transferPageAdministrationByPageId({
      sessionId,
      sessionRole,
      sessionPageId,
      pageId,
      ...transferPageAdministrationByPageIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile üyeler listelenir',
  })
  @ApiResponse({
    status: 200,
    type: SearchPageMembersByPageIdReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':pageId/members')
  async searchPageMembersByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;
    const sessionPageId = res.locals.decoded && res.locals.decoded.p_id ? res.locals.decoded.p_id : null;

    const result = await this.pageMembershipsService.searchPageMembersByPageId({
      sessionId,
      sessionRole,
      sessionPageId,
      pageId,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile eklenebilecek hesaplar listelenir',
  })
  @ApiResponse({
    status: 200,
    type: SearchUsersToAddByPageIdReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':pageId/search')
  async searchUsersToAddByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;
    const sessionPageId = res.locals.decoded && res.locals.decoded.p_id ? res.locals.decoded.p_id : null;

    const result = await this.pageMembershipsService.searchUsersToAddByPageId({
      sessionId,
      sessionRole,
      sessionPageId,
      pageId,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile yetkilileri listeler',
  })
  @ApiResponse({
    status: 200,
    type: SearchAdministrationByPageIdReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':pageId/administration')
  async searchAdministrationByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;
    const sessionPageId = res.locals.decoded && res.locals.decoded.p_id ? res.locals.decoded.p_id : null;

    const result = await this.pageMembershipsService.searchAdministrationByPageId({
      sessionId,
      sessionRole,
      sessionPageId,
      pageId,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile üye olunur',
  })
  @ApiResponse({ status: 200 })
  @Post(':pageId/join')
  async joinPageMembershipByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.pageMembershipsService.joinPageMembershipByPageId({
      sessionId,
      sessionRole,
      pageId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
  @ApiOperation({
    summary: 'Sayfa IDsi ile üyelikten çıkılır',
  })
  @ApiResponse({ status: 200 })
  @Post(':pageId/leave')
  async leavePageMembershipByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.pageMembershipsService.leavePageMembershipByPageId({
      sessionId,
      sessionRole,
      pageId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile üye eklenir',
  })
  @ApiResponse({ status: 200 })
  @Post(':pageId/add')
  async addPageMembersByPageId(
    @Body() addPageMembersByPageIdDto: AddPageMembersByPageIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;
    const sessionPageId = res.locals.decoded && res.locals.decoded.p_id ? res.locals.decoded.p_id : null;

    const result = await this.pageMembershipsService.addPageMembersByPageId({
      sessionId,
      sessionRole,
      sessionPageId,
      pageId,
      ...addPageMembersByPageIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile üyelikten çıkartılır',
  })
  @ApiResponse({ status: 200 })
  @Post(':pageId/remove')
  async removePageMemberByPageId(
    @Body()
    removePageMemberByPageIdDto: RemovePageMemberByPageIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;
    const sessionPageId = res.locals.decoded && res.locals.decoded.p_id ? res.locals.decoded.p_id : null;

    const result = await this.pageMembershipsService.removePageMemberByPageId({
      sessionId,
      sessionRole,
      sessionPageId,
      pageId,
      ...removePageMemberByPageIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
}
