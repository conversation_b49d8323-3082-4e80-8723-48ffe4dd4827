import { ApiProperty } from '@nestjs/swagger';
import { UserListItemWithGroupRole } from 'src/models';

export class CreateGroupReturn {
  @ApiProperty({
    description: 'UUID of the newly created group',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  groupId!: string;
}

export class GetGroupByGroupIdReturn {
  @ApiProperty({
    description: 'Unique identifier of the group',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  groupId!: string;

  @ApiProperty({
    description: 'Name of the group',
    example: 'Photography Enthusiasts',
  })
  groupName!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'URL to the group thumbnail image',
    example: 'https://example.com/group-thumbnail.jpg',
    format: 'url',
  })
  thumbnailUrl!: string | null;

  @ApiProperty({
    type: [UserListItemWithGroupRole],
    description: 'List of group members with their roles and friendship status',
    example: [],
  })
  members!: UserListItemWithGroupRole[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of group members',
    example: 5,
    minimum: 0,
  })
  memberCount!: number;
}
